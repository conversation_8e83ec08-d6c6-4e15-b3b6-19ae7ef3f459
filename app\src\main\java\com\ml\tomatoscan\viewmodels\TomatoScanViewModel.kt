package com.ml.tomatoscan.viewmodels































































































































import android.app.Application































































import android.content.Context



























































<<<<<<<


=======
        // Enhanced throttling with adaptive interval

        val adaptiveInterval = if (_realTimeDetectionState.value.averageProcessingTime > 1000) {

            realTimeConfig.frameProcessingInterval * 2 // Slow down if processing is slow

        } else {

            realTimeConfig.frameProcessingInterval

        }



        if (currentTime - lastFrameProcessTime < adaptiveInterval) {

            return

        }

>>>>>>>


import android.graphics.Bitmap































































import android.net.Uri































































<<<<<<<



import androidx.lifecycle.AndroidViewModel







=======



                // Check if this is an invalid image (non-tomato)







                val isInvalidImage = ensembleResult.finalDisease.equals("Invalid Image", ignoreCase = true) ||







                                   ensembleResult.geminiResult.diseaseDetected.equals("Invalid Image", ignoreCase = true) ||







                                   ensembleResult.finalDisease.contains("Invalid", ignoreCase = true) ||







                                   quality == "Invalid"







>>>>>>>











<<<<<<<











=======



                val result = if (isInvalidImage) {







                    android.util.Log.d("TomatoScanViewModel", "Detected invalid/non-tomato image, providing minimal response")







                    // Create minimal result for non-tomato images







                    ScanResult(







                        imageUrl = imageUrl,







                        quality = "Invalid",







                        confidence = 0f,







                        timestamp = Date().time,







                        diseaseDetected = "Unable to Analyze",







                        severity = "N/A",







                        description = "Unable to analyze this image. Please ensure you're uploading a clear image of a tomato leaf.",







                        recommendations = emptyList(), // No recommendations for non-tomato images







                        treatmentOptions = emptyList(), // No treatment options for non-tomato images







                        preventionMeasures = emptyList() // No prevention measures for non-tomato images







                    )







                } else {







                    // Use ensemble confidence (already in 0-1 range)







                    val finalConfidence = ensembleResult.finalConfidence







>>>>>>>











<<<<<<<











=======



                    // Generate comprehensive recommendations using the recommendation engine







                    val recommendationSet = recommendationEngine.generateRecommendations(ensembleResult)















                    // Create enhanced result with ensemble data and advanced recommendations







                    ScanResult(







                        imageUrl = imageUrl,







                        quality = quality,







                        confidence = finalConfidence,







                        timestamp = Date().time,







                        diseaseDetected = ensembleResult.finalDisease,







                        severity = ensembleResult.finalSeverity,







                        description = createEnhancedDescription(ensembleResult, recommendationSet),







                        recommendations = combineAllRecommendations(recommendationSet),







                        treatmentOptions = recommendationSet.treatmentPlan,







                        preventionMeasures = recommendationSet.preventionMeasures







                    )







                }







                







                android.util.Log.d("TomatoScanViewModel", "Analysis complete - Disease: ${result.diseaseDetected}, Severity: ${result.severity}")







                _scanResult.value = result







                







                // Save to local Room database with image







                try {







                    historyRepository.saveToHistory(result, imageUri, bitmap)







                    android.util.Log.d("TomatoScanViewModel", "Saved to Room database successfully")







                } catch (e: Exception) {







                    android.util.Log.e("TomatoScanViewModel", "Failed to save to Room database", e)







                }







                







            } catch (e: Exception) {







                android.util.Log.e("TomatoScanViewModel", "Analysis failed with error: ${e.message}", e)







                e.printStackTrace()







                







                // Create error result with disease analysis structure







                val errorResult = ScanResult(







                    imageUrl = imageUri.toString(),







                    quality = "Error",







                    confidence = 0f,







                    timestamp = Date().time,







                    diseaseDetected = "Analysis Error",







                    severity = "Unknown",







                    description = "Unable to analyze the image: ${e.message}",







                    recommendations = listOf("Please try again with a clearer image", "Ensure good lighting conditions"),







                    treatmentOptions = listOf("Consult with a local agricultural expert"),







                    preventionMeasures = listOf("Regular monitoring", "Proper plant spacing")







                )







                _scanResult.value = errorResult







            } finally {







                _isLoading.value = false







                android.util.Log.d("TomatoScanViewModel", "Loading state cleared")







            }







        }







    }







>>>>>>>



























import androidx.lifecycle.viewModelScope































































import com.ml.tomatoscan.data.FirebaseData































































import coil.ImageLoader































































import com.ml.tomatoscan.data.GeminiApi































































import com.ml.tomatoscan.data.TomatoTFLiteClassifier































































import com.ml.tomatoscan.data.EnsembleClassifier































































import com.ml.tomatoscan.data.RecommendationEngine































































import com.ml.tomatoscan.data.HistoryRepository































































import com.ml.tomatoscan.data.DetectionMode































































import com.ml.tomatoscan.data.RealTimeDetectionResult































<<<<<<<











=======



        viewModelScope.launch {







            try {







                // Validate input bitmap







                if (bitmap.isRecycled) {







                    android.util.Log.w("TomatoScanViewModel", "Bitmap is recycled, skipping frame")







                    val errorResult = RealTimeDetectionResult.error("Invalid frame")







                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(errorResult)







                    return@launch







                }















                // Check if model is ready







                android.util.Log.d("TomatoScanViewModel", "Processing real-time frame - checking model readiness...")







                if (!tfliteClassifier.isModelReady()) {







                    android.util.Log.w("TomatoScanViewModel", "Model not ready for real-time processing")







                    val notReadyResult = RealTimeDetectionResult(







                        disease = "Model Loading...",







                        confidence = 0f,







                        confidenceLevel = "Loading",







                        processingStatus = ProcessingStatus.ANALYZING







                    )







                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(notReadyResult)







                    return@launch







                }







                android.util.Log.d("TomatoScanViewModel", "Model is ready, proceeding with classification...")







>>>>>>>



























import com.ml.tomatoscan.data.RealTimeDetectionState















<<<<<<<











=======



                if (classificationResult == null) {







                    android.util.Log.w("TomatoScanViewModel", "Real-time processing timed out")







                    val timeoutResult = RealTimeDetectionResult.error("Processing timeout - try moving camera slower")







                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(timeoutResult)







                    return@launch







                }







>>>>>>>



























<<<<<<<











=======



                // Handle specific error cases from classifier







                val processingStatus = when {







                    classificationResult.disease == "Error" -> ProcessingStatus.ERROR







                    classificationResult.disease == "Processing Error" -> ProcessingStatus.ERROR







                    classificationResult.disease == "Model Loading..." -> ProcessingStatus.ANALYZING







                    classificationResult.disease == "Analyzing..." -> ProcessingStatus.ANALYZING







                    classificationResult.confidence >= realTimeConfig.confidenceThreshold -> ProcessingStatus.DETECTED







                    else -> ProcessingStatus.ANALYZING







                }















                // Create real-time result







                val realTimeResult = RealTimeDetectionResult(







                    disease = classificationResult.disease,







                    confidence = classificationResult.confidence,







                    confidenceLevel = tfliteClassifier.getConfidenceLevel(classificationResult.confidence),







                    processingStatus = processingStatus,







                    frameProcessingTime = processingTime







                )







>>>>>>>











import com.ml.tomatoscan.data.RealTimeDetectionConfig































<<<<<<<











=======



            } catch (e: Exception) {







                android.util.Log.e("TomatoScanViewModel", "Real-time processing error: ${e.message}", e)







                e.printStackTrace()







                val errorResult = RealTimeDetectionResult.error("Camera processing error")







                _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(errorResult)







            }







        }







    }







>>>>>>>



























import com.ml.tomatoscan.data.ProcessingStatus































































import com.ml.tomatoscan.models.ScanResult































































import com.ml.tomatoscan.utils.DatabaseImageFetcher































































import kotlinx.coroutines.flow.MutableStateFlow































































import kotlinx.coroutines.flow.SharingStarted































































<<<<<<<















import kotlinx.coroutines.flow.StateFlow































=======















                // Check if this is an invalid image (non-tomato)































                val isInvalidImage = ensembleResult.finalDisease.equals("Invalid Image", ignoreCase = true) ||































                                   ensembleResult.geminiResult.diseaseDetected.equals("Invalid Image", ignoreCase = true) ||































                                   ensembleResult.finalDisease.contains("Invalid", ignoreCase = true) ||































                                   quality == "Invalid"































>>>>>>>















































<<<<<<<















import kotlinx.coroutines.flow.stateIn































=======















                val result = if (isInvalidImage) {































                    android.util.Log.d("TomatoScanViewModel", "Detected invalid/non-tomato image, providing minimal response")































                    // Create minimal result for non-tomato images































                    ScanResult(































                        imageUrl = imageUrl,































                        quality = "Invalid",































                        confidence = 0f,































                        timestamp = Date().time,































                        diseaseDetected = "Unable to Analyze",































                        severity = "N/A",































                        description = "Unable to analyze this image. Please ensure you're uploading a clear image of a tomato leaf.",































                        recommendations = emptyList(), // No recommendations for non-tomato images































                        treatmentOptions = emptyList(), // No treatment options for non-tomato images































                        preventionMeasures = emptyList() // No prevention measures for non-tomato images































                    )































                } else {































                    // Use ensemble confidence (already in 0-1 range)































                    val finalConfidence = ensembleResult.finalConfidence































>>>>>>>















































<<<<<<<















import kotlinx.coroutines.flow.onStart































=======















                    // Generate comprehensive recommendations using the recommendation engine































                    val recommendationSet = recommendationEngine.generateRecommendations(ensembleResult)































































                    // Create enhanced result with ensemble data and advanced recommendations































                    ScanResult(































                        imageUrl = imageUrl,































                        quality = quality,































                        confidence = finalConfidence,































                        timestamp = Date().time,































                        diseaseDetected = ensembleResult.finalDisease,































                        severity = ensembleResult.finalSeverity,































                        description = createEnhancedDescription(ensembleResult, recommendationSet),































                        recommendations = combineAllRecommendations(recommendationSet),































                        treatmentOptions = recommendationSet.treatmentPlan,































                        preventionMeasures = recommendationSet.preventionMeasures































                    )































                }































                































                android.util.Log.d("TomatoScanViewModel", "Analysis complete - Disease: ${result.diseaseDetected}, Severity: ${result.severity}")































                _scanResult.value = result































                































                // Save to local Room database with image































                try {































                    historyRepository.saveToHistory(result, imageUri, bitmap)































                    android.util.Log.d("TomatoScanViewModel", "Saved to Room database successfully")































                } catch (e: Exception) {































                    android.util.Log.e("TomatoScanViewModel", "Failed to save to Room database", e)































                }































                































            } catch (e: Exception) {































                android.util.Log.e("TomatoScanViewModel", "Analysis failed with error: ${e.message}", e)































                e.printStackTrace()































                































                // Create error result with disease analysis structure































                val errorResult = ScanResult(































                    imageUrl = imageUri.toString(),































                    quality = "Error",































                    confidence = 0f,































                    timestamp = Date().time,































                    diseaseDetected = "Analysis Error",































                    severity = "Unknown",































                    description = "Unable to analyze the image: ${e.message}",































                    recommendations = listOf("Please try again with a clearer image", "Ensure good lighting conditions"),































                    treatmentOptions = listOf("Consult with a local agricultural expert"),































                    preventionMeasures = listOf("Regular monitoring", "Proper plant spacing")































                )































                _scanResult.value = errorResult































            } finally {































                _isLoading.value = false































                android.util.Log.d("TomatoScanViewModel", "Loading state cleared")































            }































        }































    }































>>>>>>>















































import kotlinx.coroutines.delay































































import kotlinx.coroutines.flow.onEach































































import kotlinx.coroutines.launch































































import kotlinx.coroutines.withTimeoutOrNull































































import java.util.Date































































































































class TomatoScanViewModel(application: Application) : AndroidViewModel(application) {































































































































    private val tfliteClassifier = TomatoTFLiteClassifier(application)































































    private val geminiApi = GeminiApi()































































    private val ensembleClassifier = EnsembleClassifier(application)































































    private val recommendationEngine = RecommendationEngine()































































    private val firebaseData = FirebaseData()































































    private val historyRepository: HistoryRepository = HistoryRepository(application)































































































































    val imageLoader: ImageLoader = ImageLoader.Builder(application)































































        .components {































































            add(DatabaseImageFetcher.Factory(application))































































        }































































        .build()































































































































    private val _scanResult = MutableStateFlow<ScanResult?>(null)































































    val scanResult: StateFlow<ScanResult?> = _scanResult































































































































    private val _isHistoryLoading = MutableStateFlow(false)































































    val isHistoryLoading: StateFlow<Boolean> = _isHistoryLoading































































































































    val scanHistory: StateFlow<List<ScanResult>> = historyRepository.getHistory()































































        .onStart { _isHistoryLoading.value = true }































































        .onEach { _isHistoryLoading.value = false }































































        .stateIn(































































            scope = viewModelScope,































































            started = SharingStarted.WhileSubscribed(5000),































































            initialValue = emptyList()































































        )































































































































    private val _isLoading = MutableStateFlow(false)































































    val isLoading: StateFlow<Boolean> = _isLoading































































































































<<<<<<<















    private val _isRefreshing = MutableStateFlow(false)































=======















        viewModelScope.launch {































            try {































                // Validate input bitmap































                if (bitmap.isRecycled) {































                    android.util.Log.w("TomatoScanViewModel", "Bitmap is recycled, skipping frame")































                    val errorResult = RealTimeDetectionResult.error("Invalid frame")































                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(errorResult)































                    return@launch































                }































































                // Check if model is ready































                android.util.Log.d("TomatoScanViewModel", "Processing real-time frame - checking model readiness...")































                if (!tfliteClassifier.isModelReady()) {































                    android.util.Log.w("TomatoScanViewModel", "Model not ready for real-time processing")































                    val notReadyResult = RealTimeDetectionResult(































                        disease = "Model Loading...",































                        confidence = 0f,































                        confidenceLevel = "Loading",































                        processingStatus = ProcessingStatus.ANALYZING































                    )































                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(notReadyResult)































                    return@launch































                }































                android.util.Log.d("TomatoScanViewModel", "Model is ready, proceeding with classification...")































>>>>>>>















































    val isRefreshing: StateFlow<Boolean> = _isRefreshing































































































































<<<<<<<















    private val _analysisImageUri = MutableStateFlow<Uri?>(null)































=======















                if (classificationResult == null) {































                    android.util.Log.w("TomatoScanViewModel", "Real-time processing timed out")































                    val timeoutResult = RealTimeDetectionResult.error("Processing timeout - try moving camera slower")































                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(timeoutResult)































                    return@launch































                }































>>>>>>>















































    val analysisImageUri: StateFlow<Uri?> = _analysisImageUri































































<<<<<<<















































=======















                // Handle specific error cases from classifier































                val processingStatus = when {































                    classificationResult.disease == "Error" -> ProcessingStatus.ERROR































                    classificationResult.disease == "Processing Error" -> ProcessingStatus.ERROR































                    classificationResult.disease == "Model Loading..." -> ProcessingStatus.ANALYZING































                    classificationResult.disease == "Analyzing..." -> ProcessingStatus.ANALYZING































                    classificationResult.confidence >= realTimeConfig.confidenceThreshold -> ProcessingStatus.DETECTED































                    else -> ProcessingStatus.ANALYZING































                }































































                // Create real-time result































                val realTimeResult = RealTimeDetectionResult(































                    disease = classificationResult.disease,































                    confidence = classificationResult.confidence,































                    confidenceLevel = tfliteClassifier.getConfidenceLevel(classificationResult.confidence),































                    processingStatus = processingStatus,































                    frameProcessingTime = processingTime































                )































>>>>>>>















































    private val _directCameraMode = MutableStateFlow(false)































































    val directCameraMode: StateFlow<Boolean> = _directCameraMode































































<<<<<<<















































=======















            } catch (e: Exception) {































                android.util.Log.e("TomatoScanViewModel", "Real-time processing error: ${e.message}", e)































                e.printStackTrace()































                val errorResult = RealTimeDetectionResult.error("Camera processing error")































                _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(errorResult)































            }































        }































    }































>>>>>>>















































    // Real-time detection state































































    private val _realTimeDetectionState = MutableStateFlow(RealTimeDetectionState())































































    val realTimeDetectionState: StateFlow<RealTimeDetectionState> = _realTimeDetectionState































































































































    private val _detectionMode = MutableStateFlow(DetectionMode.IMAGE_UPLOAD)































































    val detectionMode: StateFlow<DetectionMode> = _detectionMode































































































































    private val realTimeConfig = RealTimeDetectionConfig()































































    private var lastFrameProcessTime = 0L































































































































































































































































    fun setAnalysisImageUri(uri: Uri?) {































































        _analysisImageUri.value = uri































































    }































































































































    fun setDirectCameraMode(enabled: Boolean) {































































        _directCameraMode.value = enabled































































    }































































































































    fun analyzeImage(bitmap: Bitmap, imageUri: Uri) {































































        viewModelScope.launch {































































            _isLoading.value = true































































            try {































































                android.util.Log.d("TomatoScanViewModel", "Starting enhanced tomato leaf disease analysis...")































































































































                // Use the new ensemble classifier for comprehensive analysis































































                val ensembleResult = ensembleClassifier.analyzeWithEnsemble(bitmap)































































































































                android.util.Log.d("TomatoScanViewModel", "Ensemble result: ${ensembleResult.finalDisease} (${ensembleResult.finalConfidence})")































































                android.util.Log.d("TomatoScanViewModel", "Consensus level: ${ensembleResult.consensusLevel}")































































                android.util.Log.d("TomatoScanViewModel", "Decision rationale: ${ensembleResult.decisionRationale}")































































































































                // Store image locally































































                val imageUrl = imageUri.toString()































































































































                // Enhanced quality assessment based on ensemble metrics































































                val quality = determineQualityFromEnsemble(ensembleResult)































































































































                // Use ensemble confidence (already in 0-1 range)































































                val finalConfidence = ensembleResult.finalConfidence































































































































                // Generate comprehensive recommendations using the recommendation engine































































                val recommendationSet = recommendationEngine.generateRecommendations(ensembleResult)































































































































                // Create enhanced result with ensemble data and advanced recommendations































































                val result = ScanResult(































































                    imageUrl = imageUrl,































































                    quality = quality,































































                    confidence = finalConfidence,































































                    timestamp = Date().time,































































                    diseaseDetected = ensembleResult.finalDisease,































































                    severity = ensembleResult.finalSeverity,































































                    description = createEnhancedDescription(ensembleResult, recommendationSet),































































                    recommendations = combineAllRecommendations(recommendationSet),































































                    treatmentOptions = recommendationSet.treatmentPlan,































































                    preventionMeasures = recommendationSet.preventionMeasures































































                )































































                































































                android.util.Log.d("TomatoScanViewModel", "Analysis complete - Disease: ${result.diseaseDetected}, Severity: ${result.severity}")































































                _scanResult.value = result































































                































































                // Save to local Room database with image































































                try {































































                    historyRepository.saveToHistory(result, imageUri, bitmap)































































                    android.util.Log.d("TomatoScanViewModel", "Saved to Room database successfully")































































                } catch (e: Exception) {































































                    android.util.Log.e("TomatoScanViewModel", "Failed to save to Room database", e)































































                }































































                































































            } catch (e: Exception) {































































                android.util.Log.e("TomatoScanViewModel", "Analysis failed with error: ${e.message}", e)































































                e.printStackTrace()































































                































































                // Create error result with disease analysis structure































































                val errorResult = ScanResult(































































                    imageUrl = imageUri.toString(),































































                    quality = "Error",































































                    confidence = 0f,































































                    timestamp = Date().time,































































                    diseaseDetected = "Analysis Error",































































                    severity = "Unknown",































































                    description = "Unable to analyze the image: ${e.message}",































































                    recommendations = listOf("Please try again with a clearer image", "Ensure good lighting conditions"),































































                    treatmentOptions = listOf("Consult with a local agricultural expert"),































































                    preventionMeasures = listOf("Regular monitoring", "Proper plant spacing")































































                )































































                _scanResult.value = errorResult































































            } finally {































































                _isLoading.value = false































































                android.util.Log.d("TomatoScanViewModel", "Loading state cleared")































































            }































































        }































































    }































































































































    private fun createMockAnalysisResult(): com.ml.tomatoscan.data.TomatoAnalysisResult {































































        val diseases = listOf("Healthy", "Early Blight", "Late Blight", "Septoria Leaf Spot", "Bacterial Spot")































































        val severities = listOf("Healthy", "Mild", "Moderate", "Severe")































































        val confidences = listOf(95.5f, 87.2f, 78.8f, 92.1f, 85.3f)































































        































































        val randomIndex = (0..4).random()































































        val disease = diseases[randomIndex]































































        val severity = if (disease == "Healthy") "Healthy" else severities[(1..3).random()]































































        































































        return com.ml.tomatoscan.data.TomatoAnalysisResult(































































            diseaseDetected = disease,































































            confidence = confidences[randomIndex],































































            severity = severity,































































            description = when (disease) {































































                "Healthy" -> "The tomato leaf appears healthy with no visible signs of disease. Good color and structure observed."































































                "Early Blight" -> "Dark spots with concentric rings visible on leaves, characteristic of Alternaria solani infection."































































                "Late Blight" -> "Water-soaked lesions with white fuzzy growth detected, indicating Phytophthora infestans."































































                "Septoria Leaf Spot" -> "Small circular spots with gray centers and dark borders observed on leaf surface."































































                "Bacterial Spot" -> "Small, dark, greasy spots with yellow halos detected, indicating bacterial infection."































































                else -> "Analysis completed successfully."































































            },































































            recommendations = when (disease) {































































                "Healthy" -> listOf("Continue current care routine", "Monitor regularly for changes", "Maintain good air circulation")































































                "Early Blight" -> listOf("Remove affected leaves immediately", "Improve air circulation", "Apply fungicide treatment")































































                "Late Blight" -> listOf("Isolate plant immediately", "Remove all infected material", "Apply copper-based fungicide")































































                "Septoria Leaf Spot" -> listOf("Remove affected leaves", "Avoid overhead watering", "Apply preventive fungicide")































































                "Bacterial Spot" -> listOf("Remove infected plant parts", "Avoid water splash", "Apply copper bactericide")































































                else -> listOf("Monitor plant closely", "Consult agricultural expert", "Follow best practices")































































            },































































            treatmentOptions = when (disease) {































































                "Healthy" -> listOf("No treatment needed", "Preventive care only", "Regular monitoring")































































                else -> listOf("Organic fungicide spray", "Copper-based treatment", "Systemic fungicide application")































































            },































































            preventionMeasures = listOf("Proper plant spacing", "Good air circulation", "Avoid overhead watering", "Regular inspection")































































        )































































    }































































































































    private fun createMockResult(imageUri: Uri): ScanResult {































































        // Create realistic mock results for testing































































        val qualities = listOf("Excellent", "Good", "Fair", "Poor", "Unripe")































































        val confidences = listOf(95.5f, 87.2f, 78.8f, 65.3f, 92.1f)































































        































































        val randomIndex = (0..4).random()































































        































































        return ScanResult(































































            imageUrl = imageUri.toString(),































































            quality = qualities[randomIndex],































































            confidence = confidences[randomIndex],































































            timestamp = Date().time































































        )































































    }































































































































    fun clearAnalysisState() {































































        _scanResult.value = null































































        _analysisImageUri.value = null































































        _directCameraMode.value = false































































        // Reset to default mode if not in real-time































































        if (_detectionMode.value != DetectionMode.REAL_TIME_CAMERA) {































































            _detectionMode.value = DetectionMode.IMAGE_UPLOAD































































        }































































    }































































































































    fun refresh() {































































        viewModelScope.launch {































































            _isRefreshing.value = true































































            // Data is already live from Room, so we just show the indicator for a bit for good UX.































































            delay(1500)































































            _isRefreshing.value = false































































        }































































    }































































































































































































































































    fun deleteFromHistory(scanResult: ScanResult) {































































        viewModelScope.launch {































































            try {































































                historyRepository.deleteFromHistory(scanResult)































































                android.util.Log.d("TomatoScanViewModel", "Deleted item from history")































































            } catch (e: Exception) {































































                android.util.Log.e("TomatoScanViewModel", "Failed to delete from history", e)































































            }































































        }































































    }































































































































    fun clearHistory() {































































        viewModelScope.launch {































































            try {































































                historyRepository.clearHistory()































































                android.util.Log.d("TomatoScanViewModel", "Cleared all history")































































            } catch (e: Exception) {































































                android.util.Log.e("TomatoScanViewModel", "Failed to clear history", e)































































            }































































        }































































    }































































































































    private fun determineQualityFromEnsemble(ensembleResult: com.ml.tomatoscan.data.EnsembleResult): String {































































        val metrics = ensembleResult.analysisMetrics































































































































        return when {































































            ensembleResult.finalDisease.equals("Invalid Image", ignoreCase = true) -> "Invalid"































































            ensembleResult.finalDisease.equals("Error", ignoreCase = true) -> "Error"































































            metrics.overallReliability >= 0.8f && ensembleResult.consensusLevel == "High" -> "Excellent"































































            metrics.overallReliability >= 0.6f && ensembleResult.consensusLevel in listOf("High", "Medium") -> "Good"































































            metrics.overallReliability >= 0.4f -> "Fair"































































            else -> "Poor"































































        }































































    }































































































































    private fun createEnhancedDescription(































































        ensembleResult: com.ml.tomatoscan.data.EnsembleResult,































































        recommendationSet: com.ml.tomatoscan.data.RecommendationSet































































    ): String {































































        val baseDescription = ensembleResult.geminiResult.description































































        val metrics = ensembleResult.analysisMetrics































































        val severity = ensembleResult.severityAnalysis































































































































        val analysisDetails = buildString {































































            append(baseDescription)































































            append("\n\n--- Analysis Details ---\n")































































            append("• Decision Method: ${ensembleResult.decisionRationale}\n")































































            append("• Consensus Level: ${ensembleResult.consensusLevel}\n")































































            append("• Image Quality: ${String.format("%.1f", metrics.imageQuality * 100)}%\n")































































            append("• Model Agreement: ${String.format("%.1f", metrics.modelAgreement * 100)}%\n")































































            append("• Overall Reliability: ${String.format("%.1f", metrics.overallReliability * 100)}%\n")































































































































            // Enhanced severity information































































            severity?.let { sev ->































































                append("\n--- Severity Assessment ---\n")































































                append("• Severity Score: ${String.format("%.1f", sev.severityScore * 100)}%\n")































































                append("• Affected Area: ${String.format("%.1f", sev.affectedAreaPercentage)}%\n")































































                append("• Progression Stage: ${sev.progressionStage}\n")































































                append("• Urgency Level: ${sev.urgencyLevel}\n")































































                append("• Treatment Urgency: ${sev.treatmentUrgency}\n")































































                append("• Prognosis: ${sev.prognosis}\n")































































                append("• Monitoring: ${sev.monitoringFrequency}\n")































































































































                if (sev.riskFactors.isNotEmpty()) {































































                    append("• Risk Factors: ${sev.riskFactors.joinToString(", ")}\n")































































                }































































































































                append("• Expected Progression: ${sev.expectedProgression}\n")































































            }































































































































            if (ensembleResult.geminiResult.affectedArea != "Unknown") {































































                append("• Gemini Affected Area: ${ensembleResult.geminiResult.affectedArea}\n")































































            }































































































































            if (ensembleResult.geminiResult.progressionStage != "Unknown") {































































                append("• Gemini Disease Stage: ${ensembleResult.geminiResult.progressionStage}\n")































































            }































































































































            if (ensembleResult.geminiResult.environmentalFactors.isNotEmpty()) {































































                append("• Environmental Factors: ${ensembleResult.geminiResult.environmentalFactors.joinToString(", ")}\n")































































            }































































































































            // Add confidence indicators from recommendation engine































































            append("\n--- Confidence Metrics ---\n")































































            recommendationSet.confidenceIndicators.forEach { indicator ->































































                append("• $indicator\n")































































            }































































        }































































































































        return analysisDetails































































    }































































































































    private fun createEnhancedRecommendations(ensembleResult: com.ml.tomatoscan.data.EnsembleResult): List<String> {































































        val baseRecommendations = ensembleResult.geminiResult.recommendations.toMutableList()































































        val severity = ensembleResult.severityAnalysis































































































































        // Add severity-based urgent recommendations first































































        severity?.let { sev ->































































            when (sev.urgencyLevel) {































































                "Critical" -> {































































                    baseRecommendations.add(0, "🚨 CRITICAL: ${sev.treatmentUrgency}")































































                    baseRecommendations.add(1, "⏰ ${sev.monitoringFrequency}")































































                }































































                "High" -> {































































                    baseRecommendations.add(0, "⚠️ HIGH URGENCY: ${sev.treatmentUrgency}")































































                    baseRecommendations.add(1, "📅 ${sev.monitoringFrequency}")































































                }































































                "Medium" -> {































































                    baseRecommendations.add(0, "📋 MODERATE URGENCY: ${sev.treatmentUrgency}")































































                }































































                "Low" -> {































































                    baseRecommendations.add(0, "ℹ️ LOW URGENCY: ${sev.treatmentUrgency}")































































                }































































            }































































































































            // Add prognosis information































































            baseRecommendations.add("🔮 Prognosis: ${sev.prognosis}")































































































































            // Add progression warning































































            if (sev.expectedProgression.contains("rapid", ignoreCase = true)) {































































                baseRecommendations.add("⚡ Warning: ${sev.expectedProgression}")































































            }































































        }































































































































        // Add priority-based recommendations































































        when (ensembleResult.recommendationPriority) {































































            "High" -> {































































                baseRecommendations.add("✅ HIGH CONFIDENCE: This analysis has high reliability - follow recommendations immediately")































































            }































































            "Medium" -> {































































                baseRecommendations.add("🔍 MEDIUM CONFIDENCE: Analysis is moderately reliable - monitor closely and implement recommendations")































































            }































































            "Low" -> {































































                baseRecommendations.add("⚠️ LOW CONFIDENCE: Analysis has uncertainty - consider getting a second opinion or retaking the image")































































            }































































        }































































































































        // Add consensus-based recommendations































































        when (ensembleResult.consensusLevel) {































































            "High" -> {































































                baseRecommendations.add("🤝 Both AI models agree on this diagnosis")































































            }































































            "Medium" -> {































































                baseRecommendations.add("🔍 AI models show moderate agreement - diagnosis is likely accurate")































































            }































































            "Low", "Very Low" -> {































































                baseRecommendations.add("⚠️ AI models disagree or are uncertain - consider professional consultation")































































                baseRecommendations.add("📸 Try retaking the image with better lighting and focus")































































            }































































        }































































































































        // Add differential diagnosis if available































































        if (ensembleResult.geminiResult.differentialDiagnosis.isNotEmpty()) {































































            baseRecommendations.add("🔬 Also consider: ${ensembleResult.geminiResult.differentialDiagnosis.joinToString(", ")}")































































        }































































































































        return baseRecommendations































































    }































































































































    private fun combineAllRecommendations(recommendationSet: com.ml.tomatoscan.data.RecommendationSet): List<String> {































































        val allRecommendations = mutableListOf<String>()































































































































        // Add immediate actions first (highest priority)































































        if (recommendationSet.immediateActions.isNotEmpty()) {































































            allRecommendations.add("🚨 IMMEDIATE ACTIONS:")































































            allRecommendations.addAll(recommendationSet.immediateActions)































































            allRecommendations.add("") // Empty line for separation































































        }































































































































        // Add monitoring guidance































































        if (recommendationSet.monitoringGuidance.isNotEmpty()) {































































            allRecommendations.add("📊 MONITORING GUIDANCE:")































































            allRecommendations.addAll(recommendationSet.monitoringGuidance)































































            allRecommendations.add("") // Empty line for separation































































        }































































































































        // Add expert consultation if needed































































        if (recommendationSet.expertConsultation.isNotEmpty()) {































































            allRecommendations.add("👨‍⚕️ EXPERT CONSULTATION:")































































            allRecommendations.addAll(recommendationSet.expertConsultation)































































            allRecommendations.add("") // Empty line for separation































































        }































































































































        return allRecommendations.filter { it.isNotBlank() } // Remove empty lines at the end































































    }































































































































    // Real-time detection methods































































    fun startRealTimeDetection() {































































        _detectionMode.value = DetectionMode.REAL_TIME_CAMERA































































        _realTimeDetectionState.value = _realTimeDetectionState.value.copy(































































            isActive = true,































































            detectionMode = DetectionMode.REAL_TIME_CAMERA































































        )































































        android.util.Log.d("TomatoScanViewModel", "Real-time detection started")































































    }































































































































    fun stopRealTimeDetection() {































































        _detectionMode.value = DetectionMode.IMAGE_UPLOAD































































        _realTimeDetectionState.value = RealTimeDetectionState()































































        android.util.Log.d("TomatoScanViewModel", "Real-time detection stopped")































































    }































































































































    fun processRealTimeFrame(bitmap: Bitmap) {































































        val currentTime = System.currentTimeMillis()































































































































        // Throttle frame processing based on config































































        if (currentTime - lastFrameProcessTime < realTimeConfig.frameProcessingInterval) {































































            return































































        }































































































































        // Skip processing if already processing































































        if (_realTimeDetectionState.value.isProcessing) {































































            return































































        }































































































































        lastFrameProcessTime = currentTime































































































































        viewModelScope.launch {































































            try {































































                // Check if model is ready































































                android.util.Log.d("TomatoScanViewModel", "Processing real-time frame - checking model readiness...")































































                if (!tfliteClassifier.isModelReady()) {































































                    android.util.Log.w("TomatoScanViewModel", "Model not ready for real-time processing")































































                    android.util.Log.w("TomatoScanViewModel", "Setting detection result to error state")































































                    _realTimeDetectionState.value = _realTimeDetectionState.value.copy(































































                        currentResult = RealTimeDetectionResult.error("Model not loaded")































































                    )































































                    return@launch































































                }































































                android.util.Log.d("TomatoScanViewModel", "Model is ready, proceeding with classification...")































































































































                // Update state to show processing































































                val processingResult = RealTimeDetectionResult.analyzing()































































                _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(processingResult)































































































































                // Perform real-time classification with timeout































































                val startTime = System.currentTimeMillis()































































                val classificationResult = withTimeoutOrNull(realTimeConfig.maxProcessingTime) {































































                    tfliteClassifier.classifyRealTime(bitmap)































































                }































































































































                if (classificationResult == null) {































































                    android.util.Log.w("TomatoScanViewModel", "Real-time processing timed out")































































                    val timeoutResult = RealTimeDetectionResult.error("Processing timeout")































































                    _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(timeoutResult)































































                    return@launch































































                }































































































































                val processingTime = System.currentTimeMillis() - startTime































































































































                // Create real-time result































































                val realTimeResult = RealTimeDetectionResult(































































                    disease = classificationResult.disease,































































                    confidence = classificationResult.confidence,































































                    confidenceLevel = tfliteClassifier.getConfidenceLevel(classificationResult.confidence),































































                    processingStatus = when {































































                        classificationResult.disease == "Error" -> ProcessingStatus.ERROR































































                        classificationResult.disease == "Analyzing..." -> ProcessingStatus.ANALYZING































































                        classificationResult.confidence >= realTimeConfig.confidenceThreshold -> ProcessingStatus.DETECTED































































                        else -> ProcessingStatus.ANALYZING































































                    },































































                    frameProcessingTime = processingTime































































                )































































































































                // Update state with new result































































                _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(realTimeResult)































































































































                android.util.Log.d("TomatoScanViewModel",































































                    "Real-time detection: ${realTimeResult.disease} (${realTimeResult.confidence}) - ${processingTime}ms")































































































































            } catch (e: Exception) {































































                android.util.Log.e("TomatoScanViewModel", "Real-time processing error", e)































































                val errorResult = RealTimeDetectionResult.error("Processing failed: ${e.message}")































































                _realTimeDetectionState.value = _realTimeDetectionState.value.withNewResult(errorResult)































































            }































































        }































































    }































































































































    fun switchToImageUploadMode() {































































        stopRealTimeDetection()































































        _detectionMode.value = DetectionMode.IMAGE_UPLOAD































































    }































































































































    fun switchToRealTimeMode() {































































        android.util.Log.d("TomatoScanViewModel", "Switching to real-time mode")































































        clearAnalysisState()































































        startRealTimeDetection()































































        android.util.Log.d("TomatoScanViewModel", "Real-time mode activated, current mode: ${_detectionMode.value}")































































    }































































































































    fun isModelReady(): Boolean {































































        return tfliteClassifier.isModelReady()































































    }































































}































































