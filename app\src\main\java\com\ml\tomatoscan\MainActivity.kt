package com.ml.tomatoscan

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue

import com.ml.tomatoscan.ui.theme.TomatoScanTheme
import com.ml.tomatoscan.viewmodels.ThemeViewModel
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.ml.tomatoscan.viewmodels.LanguageViewModel
import com.ml.tomatoscan.viewmodels.LanguageViewModelFactory
import com.ml.tomatoscan.viewmodels.ThemeViewModelFactory
import java.util.Locale

class MainActivity : ComponentActivity() {

    private val themeViewModel: ThemeViewModel by viewModels {
        ThemeViewModelFactory(application)
    }

    private val languageViewModel: LanguageViewModel by viewModels {
        LanguageViewModelFactory(application)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            val theme by themeViewModel.theme.collectAsState()
            val language by languageViewModel.language.collectAsState()

            SetLocale(language.code)

            TomatoScanTheme(theme = theme) {
                Navigation(themeViewModel = themeViewModel, languageViewModel = languageViewModel)
            }
        }
    }
}

@Composable
fun SetLocale(languageCode: String) {
    val context = LocalContext.current
    val resources = context.resources
    val configuration = resources.configuration
    val locale = Locale(languageCode)
    Locale.setDefault(locale)
    configuration.setLocale(locale)
    resources.updateConfiguration(configuration, resources.displayMetrics)
}