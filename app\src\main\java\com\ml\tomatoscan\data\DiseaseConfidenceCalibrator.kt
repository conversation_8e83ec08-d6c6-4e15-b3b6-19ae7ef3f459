package com.ml.tomatoscan.data

import android.graphics.Bitmap
import android.util.Log
import kotlin.math.max
import kotlin.math.min

/**
 * Calibrates confidence scores for disease detection, especially addressing
 * Early Blight over-detection bias in TensorFlow Lite models
 */
class DiseaseConfidenceCalibrator {
    
    // Known bias factors for different diseases
    private val diseaseCalibrationFactors = mapOf(
        "Early Blight" to 0.7f,  // Reduce confidence due to over-detection
        "Late Blight" to 1.0f,
        "Septoria Leaf Spot" to 1.0f,
        "Bacterial Spot" to 1.0f,
        "Mosaic Virus" to 1.0f,
        "Powdery Mildew" to 1.0f,
        "Fusarium Wilt" to 1.0f,
        "Leaf Curl" to 1.0f,
        "Nutrient Deficiency" to 1.0f,
        "Healthy" to 1.0f
    )
    
    // Confidence thresholds for different diseases
    private val diseaseThresholds = mapOf(
        "Early Blight" to 0.8f,  // Higher threshold due to bias
        "Late Blight" to 0.6f,
        "Septoria Leaf Spot" to 0.6f,
        "Bacterial Spot" to 0.6f,
        "Mosaic Virus" to 0.6f,
        "Powdery Mildew" to 0.6f,
        "Fusarium Wilt" to 0.6f,
        "Leaf Curl" to 0.6f,
        "Nutrient Deficiency" to 0.6f,
        "Healthy" to 0.5f
    )
    
    data class CalibratedResult(
        val originalDisease: String,
        val originalConfidence: Float,
        val calibratedDisease: String,
        val calibratedConfidence: Float,
        val calibrationReason: String,
        val isReliable: Boolean
    )
    
    /**
     * Calibrates TensorFlow Lite results based on known biases
     */
    fun calibrateTFLiteResult(result: ClassificationResult): CalibratedResult {
        val disease = result.disease
        val confidence = result.confidence
        
        // Get calibration factor for this disease
        val calibrationFactor = diseaseCalibrationFactors[disease] ?: 1.0f
        val threshold = diseaseThresholds[disease] ?: 0.6f
        
        // Apply calibration
        val calibratedConfidence = min(1.0f, confidence * calibrationFactor)
        
        // Check if result meets threshold
        val meetsThreshold = calibratedConfidence >= threshold
        
        val (finalDisease, finalConfidence, reason) = when {
            // Early Blight special handling
            disease.equals("Early Blight", ignoreCase = true) -> {
                if (calibratedConfidence >= 0.8f && result.preprocessingQuality >= 0.7f) {
                    Triple(disease, calibratedConfidence, "Early Blight confirmed with high confidence and good image quality")
                } else {
                    // Suggest alternative diagnosis or uncertainty
                    val alternativeDisease = suggestAlternativeToEarlyBlight(result)
                    Triple(
                        alternativeDisease,
                        calibratedConfidence * 0.6f,
                        "Early Blight confidence reduced due to known model bias - suggesting $alternativeDisease"
                    )
                }
            }
            
            // Other diseases
            meetsThreshold -> {
                Triple(disease, calibratedConfidence, "Confidence meets threshold for $disease")
            }
            
            else -> {
                Triple("Uncertain", calibratedConfidence, "Confidence below threshold for reliable $disease detection")
            }
        }
        
        return CalibratedResult(
            originalDisease = disease,
            originalConfidence = confidence,
            calibratedDisease = finalDisease,
            calibratedConfidence = finalConfidence,
            calibrationReason = reason,
            isReliable = meetsThreshold && finalDisease != "Uncertain"
        )
    }
    
    /**
     * Suggests alternative diagnoses when Early Blight confidence is low
     */
    private fun suggestAlternativeToEarlyBlight(result: ClassificationResult): String {
        val allConfidences = result.allConfidences
        
        // Find second highest confidence
        val sortedIndices = allConfidences.indices.sortedByDescending { allConfidences[it] }
        
        if (sortedIndices.size > 1) {
            val secondHighestIdx = sortedIndices[1]
            val secondHighestConfidence = allConfidences[secondHighestIdx]
            
            // If second highest is reasonably close, suggest it
            if (secondHighestConfidence >= 0.3f) {
                val labels = listOf(
                    "Healthy",
                    "Early Blight",
                    "Late Blight",
                    "Septoria Leaf Spot",
                    "Bacterial Spot",
                    "Mosaic Virus",
                    "Powdery Mildew",
                    "Fusarium Wilt",
                    "Leaf Curl",
                    "Nutrient Deficiency"
                )
                
                if (secondHighestIdx < labels.size) {
                    return labels[secondHighestIdx]
                }
            }
        }
        
        // Default alternatives based on common misclassifications
        return when {
            result.preprocessingQuality < 0.5f -> "Uncertain - Poor Image Quality"
            else -> "Healthy"
        }
    }
    
    /**
     * Validates Gemini results for consistency
     */
    fun validateGeminiResult(result: TomatoAnalysisResult, tfliteResult: ClassificationResult): CalibratedResult {
        val disease = result.diseaseDetected
        val confidence = result.confidence / 100f
        
        val reason = when {
            disease.equals("Early Blight", ignoreCase = true) -> {
                if (confidence >= 0.8f) {
                    "Gemini confirms Early Blight with high confidence"
                } else {
                    "Gemini suggests Early Blight but with moderate confidence"
                }
            }
            
            disease.equals("Invalid Image", ignoreCase = true) -> {
                "Gemini detected invalid or poor quality image"
            }
            
            confidence >= 0.8f -> {
                "Gemini high confidence diagnosis"
            }
            
            else -> {
                "Gemini moderate confidence diagnosis"
            }
        }
        
        return CalibratedResult(
            originalDisease = disease,
            originalConfidence = confidence,
            calibratedDisease = disease,
            calibratedConfidence = confidence,
            calibrationReason = reason,
            isReliable = confidence >= 0.6f && !disease.equals("Invalid Image", ignoreCase = true)
        )
    }
    
    /**
     * Combines calibrated results from both models
     */
    fun combineResults(
        tfliteCalibrated: CalibratedResult,
        geminiCalibrated: CalibratedResult,
        imageQuality: Float
    ): CalibratedResult {
        
        // Special handling for Early Blight
        val isEarlyBlightCase = tfliteCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true) ||
                               geminiCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true)
        
        return when {
            // Both agree on Early Blight with high confidence
            tfliteCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true) &&
            geminiCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true) &&
            geminiCalibrated.calibratedConfidence >= 0.8f -> {
                
                val avgConfidence = (tfliteCalibrated.calibratedConfidence + geminiCalibrated.calibratedConfidence) / 2f
                CalibratedResult(
                    originalDisease = "Early Blight",
                    originalConfidence = avgConfidence,
                    calibratedDisease = "Early Blight",
                    calibratedConfidence = avgConfidence,
                    calibrationReason = "Both models confirm Early Blight with high Gemini confidence",
                    isReliable = true
                )
            }
            
            // TFLite says Early Blight but Gemini disagrees
            tfliteCalibrated.originalDisease.equals("Early Blight", ignoreCase = true) &&
            !geminiCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true) -> {
                
                CalibratedResult(
                    originalDisease = tfliteCalibrated.originalDisease,
                    originalConfidence = tfliteCalibrated.originalConfidence,
                    calibratedDisease = geminiCalibrated.calibratedDisease,
                    calibratedConfidence = geminiCalibrated.calibratedConfidence,
                    calibrationReason = "TFLite Early Blight bias detected - preferring Gemini diagnosis",
                    isReliable = geminiCalibrated.isReliable
                )
            }
            
            // Prefer Gemini for high confidence results
            geminiCalibrated.calibratedConfidence >= 0.8f -> {
                geminiCalibrated.copy(
                    calibrationReason = "High confidence Gemini diagnosis preferred"
                )
            }
            
            // Prefer TFLite for non-Early Blight high confidence results
            tfliteCalibrated.isReliable && 
            !tfliteCalibrated.calibratedDisease.equals("Early Blight", ignoreCase = true) &&
            tfliteCalibrated.calibratedConfidence >= 0.8f -> {
                tfliteCalibrated.copy(
                    calibrationReason = "High confidence TFLite diagnosis (non-Early Blight)"
                )
            }
            
            // Default to Gemini for better accuracy
            else -> {
                geminiCalibrated.copy(
                    calibrationReason = "Gemini preferred for expert visual analysis"
                )
            }
        }
    }
}
