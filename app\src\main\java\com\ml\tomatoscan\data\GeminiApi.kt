package com.ml.tomatoscan.data

import android.graphics.Bitmap
import android.util.Log
import com.google.ai.client.generativeai.GenerativeModel
import com.google.ai.client.generativeai.type.content
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONArray
import org.json.JSONObject

data class TomatoAnalysisResult(
    val diseaseDetected: String,
    val confidence: Float,
    val severity: String,
    val description: String,
    val recommendations: List<String>,
    val treatmentOptions: List<String>,
    val preventionMeasures: List<String>,
    val analysisQuality: String = "Unknown",
    val urgencyLevel: String = "Low",
    val affectedArea: String = "Unknown",
    val progressionStage: String = "Unknown",
    val environmentalFactors: List<String> = emptyList(),
    val differentialDiagnosis: List<String> = emptyList()
)

class GeminiApi {

    companion object {
        private const val API_KEY = "AIzaSyBD15s-m0ClELhAR7XbbVPRkSFlQzcu_fQ"

        private val generativeModel by lazy {
            try {
                GenerativeModel(
                    modelName = "gemini-1.5-flash",
                    apiKey = API_KEY
                )
            } catch (e: Exception) {
                Log.e("GeminiApi", "Failed to initialize Gemini model", e)
                null
            }
        }
    }

    suspend fun analyzeTomatoLeaf(bitmap: Bitmap, tfliteDisease: String? = null, tfliteConfidence: Float? = null): TomatoAnalysisResult {
        return withContext(Dispatchers.IO) {
            try {
                val tfliteContext = if (!tfliteDisease.isNullOrBlank()) {
                    "\n\nLocal AI Model Results: The on-device TensorFlow Lite model classified this as '$tfliteDisease' with ${String.format("%.1f", (tfliteConfidence ?: 0f) * 100)}% confidence. Use this as supporting evidence, but prioritize your visual analysis."
                } else {
                    ""
                }

                val prompt = """
                    You are Dr. PlantPath, a world-renowned agricultural pathologist with 20+ years of experience in tomato disease diagnosis. You have published over 100 research papers on plant pathology and have helped thousands of farmers worldwide.

                    CRITICAL FIRST STEP: Verify this is a tomato leaf image.
                    $tfliteContext

                    IMPORTANT: The local TensorFlow Lite model has a known bias toward over-detecting "Early Blight". Be especially critical when evaluating Early Blight diagnoses. Only confirm Early Blight if you see CLEAR, UNMISTAKABLE symptoms.

                    If the image is NOT a tomato leaf, unclear, or of poor quality, respond with:
                    {
                        "diseaseDetected": "Invalid Image",
                        "confidence": 100.0,
                        "severity": "Unknown",
                        "analysisQuality": "Poor",
                        "urgencyLevel": "None",
                        "affectedArea": "N/A",
                        "progressionStage": "N/A",
                        "description": "Image validation failed. Please provide a clear, well-lit image of a tomato leaf.",
                        "recommendations": ["Upload a clear tomato leaf image", "Ensure good lighting", "Focus on leaf details"],
                        "treatmentOptions": [],
                        "preventionMeasures": [],
                        "environmentalFactors": [],
                        "differentialDiagnosis": []
                    }

                    For VALID tomato leaf images, provide comprehensive analysis:

                    DISEASE IDENTIFICATION PROTOCOL:
                    1. Visual Symptom Analysis:
                       - Spot patterns, colors, shapes, distribution
                       - Leaf discoloration, wilting, curling
                       - Fungal growth, bacterial ooze, viral patterns
                       - Necrosis, chlorosis, lesion characteristics

                    2. EARLY BLIGHT SPECIFIC CRITERIA (Alternaria solani):
                       REQUIRED for Early Blight diagnosis:
                       ✓ Dark brown to black circular spots with DISTINCT concentric rings (target-like pattern)
                       ✓ Spots typically 1-15mm diameter with yellow halos
                       ✓ Lesions start on older, lower leaves first
                       ✓ Bull's-eye or target pattern clearly visible
                       ✓ Spots may have dark centers with lighter rings

                       DO NOT diagnose Early Blight if you see:
                       ✗ Simple brown spots without concentric rings
                       ✗ Irregular shaped lesions
                       ✗ Spots without target pattern
                       ✗ Only yellowing without distinct spots
                       ✗ Uniform discoloration

                    3. Disease Categories to Consider:
                       FUNGAL: Early Blight (requires target spots), Late Blight, Septoria Leaf Spot, Powdery Mildew, Anthracnose, Fusarium Wilt
                       BACTERIAL: Bacterial Spot, Bacterial Speck, Bacterial Canker
                       VIRAL: Tomato Mosaic Virus, Cucumber Mosaic Virus, Leaf Curl Virus
                       PHYSIOLOGICAL: Nutrient deficiencies (N, P, K, Mg, Fe), Water stress, Heat stress
                       PEST-RELATED: Thrips damage, Mite damage, Aphid damage

                    4. Severity Assessment:
                       - Healthy: No visible symptoms
                       - Mild: <25% leaf area affected, early symptoms
                       - Moderate: 25-50% affected, spreading symptoms
                       - Severe: >50% affected, advanced symptoms, plant stress evident

                    5. Urgency Classification:
                       - Low: Healthy or very mild symptoms
                       - Medium: Moderate symptoms, monitor closely
                       - High: Severe symptoms, immediate action needed
                       - Critical: Plant survival threatened, emergency treatment

                    Respond in this EXACT JSON structure:
                    {
                        "diseaseDetected": "Specific disease name or 'Healthy'",
                        "confidence": 85.5,
                        "severity": "Healthy/Mild/Moderate/Severe",
                        "analysisQuality": "Excellent/Good/Fair/Poor",
                        "urgencyLevel": "Low/Medium/High/Critical",
                        "affectedArea": "Percentage and location description",
                        "progressionStage": "Early/Developing/Advanced/Late",
                        "description": "Detailed professional assessment with specific visual observations",
                        "recommendations": [
                            "Immediate action with timeframe",
                            "Monitoring instructions",
                            "Follow-up steps"
                        ],
                        "treatmentOptions": [
                            "Primary treatment with application method",
                            "Alternative treatment option",
                            "Organic/chemical options with specifics"
                        ],
                        "preventionMeasures": [
                            "Cultural practices",
                            "Environmental management",
                            "Preventive treatments"
                        ],
                        "environmentalFactors": [
                            "Contributing conditions",
                            "Risk factors identified"
                        ],
                        "differentialDiagnosis": [
                            "Alternative possible diagnoses",
                            "Similar conditions to rule out"
                        ]
                    }
                """.trimIndent()

                val inputContent = content {
                    image(bitmap)
                    text(prompt)
                }

                if (generativeModel == null) {
                    throw Exception("Gemini API not properly configured")
                }
                
                Log.d("GeminiApi", "Sending request to Gemini API...")
                val response = generativeModel!!.generateContent(inputContent)
                val responseText = response.text ?: "{}"
                Log.d("GeminiApi", "Received response: $responseText")

                // Clean the response text to ensure it's valid JSON
                val cleanedResponse = cleanJsonResponse(responseText)
                val jsonResponse = JSONObject(cleanedResponse)

                // Parse the enhanced response
                val diseaseDetected = jsonResponse.optString("diseaseDetected", "Unknown")
                val confidence = jsonResponse.optDouble("confidence", 0.0).toFloat()
                val severity = jsonResponse.optString("severity", "Unknown")
                val description = jsonResponse.optString("description", "No description available")
                val analysisQuality = jsonResponse.optString("analysisQuality", "Unknown")
                val urgencyLevel = jsonResponse.optString("urgencyLevel", "Low")
                val affectedArea = jsonResponse.optString("affectedArea", "Unknown")
                val progressionStage = jsonResponse.optString("progressionStage", "Unknown")

                val recommendations = parseJsonArray(jsonResponse.optJSONArray("recommendations"))
                val treatmentOptions = parseJsonArray(jsonResponse.optJSONArray("treatmentOptions"))
                val preventionMeasures = parseJsonArray(jsonResponse.optJSONArray("preventionMeasures"))
                val environmentalFactors = parseJsonArray(jsonResponse.optJSONArray("environmentalFactors"))
                val differentialDiagnosis = parseJsonArray(jsonResponse.optJSONArray("differentialDiagnosis"))

                TomatoAnalysisResult(
                    diseaseDetected = diseaseDetected,
                    confidence = confidence,
                    severity = severity,
                    description = description,
                    recommendations = recommendations,
                    treatmentOptions = treatmentOptions,
                    preventionMeasures = preventionMeasures,
                    analysisQuality = analysisQuality,
                    urgencyLevel = urgencyLevel,
                    affectedArea = affectedArea,
                    progressionStage = progressionStage,
                    environmentalFactors = environmentalFactors,
                    differentialDiagnosis = differentialDiagnosis
                )

            } catch (e: Exception) {
                Log.e("GeminiApi", "Error analyzing tomato leaf", e)
                // Return a comprehensive fallback result
                TomatoAnalysisResult(
                    diseaseDetected = "Analysis Error",
                    confidence = 0f,
                    severity = "Unknown",
                    description = "Unable to analyze the image: ${e.message}. This may be due to network connectivity, image quality, or API limitations.",
                    recommendations = listOf(
                        "Check internet connection and try again",
                        "Ensure image is clear and well-lit",
                        "Try capturing image in better lighting conditions"
                    ),
                    treatmentOptions = listOf(
                        "Consult with a local agricultural extension agent",
                        "Contact a plant pathology expert",
                        "Visit your local garden center for advice"
                    ),
                    preventionMeasures = listOf(
                        "Maintain proper plant spacing for air circulation",
                        "Water at soil level to avoid leaf wetness",
                        "Regular monitoring for early detection"
                    ),
                    analysisQuality = "Error",
                    urgencyLevel = "Unknown",
                    affectedArea = "Cannot determine",
                    progressionStage = "Unknown",
                    environmentalFactors = listOf("Analysis failed - environmental assessment unavailable"),
                    differentialDiagnosis = listOf("Professional diagnosis recommended")
                )
            }
        }
    }

    suspend fun analyzeWithConfidenceThreshold(bitmap: Bitmap, tfliteResult: ClassificationResult): TomatoAnalysisResult {
        return withContext(Dispatchers.IO) {
            try {
                // Enhanced analysis that considers TFLite confidence levels
                val confidenceContext = when {
                    tfliteResult.isHighConfidence ->
                        "High-confidence local model result: ${tfliteResult.disease} (${String.format("%.1f", tfliteResult.confidence * 100)}%). This is likely accurate - please verify and provide additional insights."
                    tfliteResult.confidence > 0.5f ->
                        "Moderate-confidence local model result: ${tfliteResult.disease} (${String.format("%.1f", tfliteResult.confidence * 100)}%). Please provide independent analysis and compare findings."
                    else ->
                        "Low-confidence local model result: ${tfliteResult.disease} (${String.format("%.1f", tfliteResult.confidence * 100)}%). Local model is uncertain - your expert analysis is crucial."
                }

                val qualityContext = "Image preprocessing quality: ${String.format("%.1f", tfliteResult.preprocessingQuality * 100)}%"

                analyzeTomatoLeaf(bitmap, tfliteResult.disease, tfliteResult.confidence)

            } catch (e: Exception) {
                Log.e("GeminiApi", "Error in confidence-based analysis", e)
                analyzeTomatoLeaf(bitmap, tfliteResult.disease, tfliteResult.confidence)
            }
        }
    }

    private fun cleanJsonResponse(response: String): String {
        // Remove any markdown formatting or extra text
        var cleaned = response.trim()
        
        // Find the JSON part (between first { and last })
        val firstBrace = cleaned.indexOf('{')
        val lastBrace = cleaned.lastIndexOf('}')
        
        if (firstBrace != -1 && lastBrace != -1 && lastBrace > firstBrace) {
            cleaned = cleaned.substring(firstBrace, lastBrace + 1)
        }
        
        return cleaned
    }

    private fun parseJsonArray(jsonArray: JSONArray?): List<String> {
        if (jsonArray == null) return emptyList()
        
        val list = mutableListOf<String>()
        for (i in 0 until jsonArray.length()) {
            list.add(jsonArray.optString(i, ""))
        }
        return list.filter { it.isNotBlank() }
    }

    // Legacy method for backward compatibility - converts new result to old format
    suspend fun analyzeImage(bitmap: Bitmap): Pair<String, Float> {
        val result = analyzeTomatoLeaf(bitmap)
        val quality = when {
            result.diseaseDetected.equals("Healthy", ignoreCase = true) -> "Excellent"
            result.severity.equals("Mild", ignoreCase = true) -> "Good"
            result.severity.equals("Moderate", ignoreCase = true) -> "Fair"
            else -> "Poor"
        }
        return Pair(quality, result.confidence)
    }
}
