<resources>
    <string name="app_name">TomatoScan</string>

    <!-- Settings Screen -->
    <string name="settings">Settings</string>
    <string name="back">Back</string>

    <!-- Account Section -->
    <string name="account">Account</string>
    <string name="change_name">Change Name</string>
    <string name="current_name">Current name: %s</string>

    <!-- Application Section -->
    <string name="application">Application</string>
    <string name="theme">Theme</string>
    <string name="customize_app_appearance">Customize app appearance</string>
    <string name="current_theme">Current: %s</string>
    <string name="notifications">Notifications</string>
    <string name="manage_notification_preferences">Manage notification preferences</string>
    <string name="language">Language</string>
    <string name="choose_your_preferred_language">Choose your preferred language</string>
    <string name="current_language">Current: %s</string>

    <!-- Data & Privacy Section -->
    <string name="data_privacy">Data &amp; Privacy</string>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="view_our_privacy_policy">View our privacy policy</string>
    <string name="clear_data">Clear Data</string>
    <string name="remove_all_local_data">Remove all local data</string>

    <!-- About Section -->
    <string name="about">About</string>
    <string name="about_tomatoscan">About TomatoScan</string>
    <string name="version">Version 1.0.0</string>
    <string name="rate_app">Rate App</string>
    <string name="rate_us_on_play_store">Rate us on Play Store</string>

    <!-- Dialogs -->
    <string name="close">Close</string>
    <string name="about_dialog_title">About TomatoScan</string>
    <string name="about_dialog_desc">An AI-powered app to help you identify tomato leaf diseases and get treatment advice.</string>
    <string name="powered_by_gemini">Powered by Google Gemini AI</string>
    <string name="choose_theme">Choose Theme</string>
    <string name="system_default">System Default</string>
    <string name="light">Light</string>
    <string name="dark">Dark</string>
    <string name="change_your_name">Change Your Name</string>
    <string name="name">Name</string>
    <string name="save">Save</string>
    <string name="cancel">Cancel</string>
    <string name="choose_language">Choose Language</string>
    <string name="language_english">English</string>
    <string name="language_filipino">Filipino</string>
    <string name="language_bisaya">Bisaya</string>
</resources>