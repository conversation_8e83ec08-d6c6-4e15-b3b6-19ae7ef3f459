package com.ml.tomatoscan.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Color
import android.util.Log
import com.ml.tomatoscan.data.TomatoTFLiteClassifier
import com.ml.tomatoscan.data.DetectionMode
import com.ml.tomatoscan.data.RealTimeDetectionState
import com.ml.tomatoscan.data.ProcessingStatus

/**
 * Utility class for testing real-time detection functionality
 */
object RealTimeDetectionTest {
    
    private const val TAG = "RealTimeDetectionTest"
    
    /**
     * Test the TensorFlow Lite model loading and basic inference
     */
    fun testModelLoading(context: Context): Boolean {
        return try {
            val classifier = TomatoTFLiteClassifier(context)
            val isReady = classifier.isModelReady()
            Log.d(TAG, "Model ready: $isReady")
            
            if (isReady) {
                // Test with a dummy bitmap
                val testBitmap = createTestBitmap()
                val result = classifier.classifyRealTime(testBitmap)
                Log.d(TAG, "Test classification result: ${result.disease} (${result.confidence})")
                true
            } else {
                Log.e(TAG, "Model failed to load")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "Model loading test failed", e)
            false
        }
    }
    
    /**
     * Test real-time detection state management
     */
    fun testRealTimeDetectionState(): Boolean {
        return try {
            val initialState = RealTimeDetectionState()
            Log.d(TAG, "Initial state: isActive=${initialState.isActive}, mode=${initialState.detectionMode}")
            
            val activeState = initialState.copy(
                isActive = true,
                detectionMode = DetectionMode.REAL_TIME_CAMERA
            )
            Log.d(TAG, "Active state: isActive=${activeState.isActive}, mode=${activeState.detectionMode}")
            
            // Test result processing
            val testResult = com.ml.tomatoscan.data.RealTimeDetectionResult(
                disease = "Test Disease",
                confidence = 0.85f,
                confidenceLevel = "High",
                processingStatus = ProcessingStatus.DETECTED,
                frameProcessingTime = 150L
            )
            
            val updatedState = activeState.withNewResult(testResult)
            Log.d(TAG, "Updated state: frameCount=${updatedState.frameCount}, avgTime=${updatedState.averageProcessingTime}")
            
            true
        } catch (e: Exception) {
            Log.e(TAG, "Real-time detection state test failed", e)
            false
        }
    }
    
    /**
     * Test performance metrics
     */
    fun testPerformanceMetrics(): Boolean {
        return try {
            val state = RealTimeDetectionState()
            val startTime = System.currentTimeMillis()
            
            // Simulate multiple frame processing results
            var currentState = state
            for (i in 1..10) {
                val result = com.ml.tomatoscan.data.RealTimeDetectionResult(
                    disease = "Test Disease $i",
                    confidence = 0.5f + (i * 0.05f),
                    confidenceLevel = "Medium",
                    processingStatus = ProcessingStatus.DETECTED,
                    frameProcessingTime = 100L + (i * 10L)
                )
                currentState = currentState.withNewResult(result)
            }
            
            val endTime = System.currentTimeMillis()
            Log.d(TAG, "Performance test completed in ${endTime - startTime}ms")
            Log.d(TAG, "Final state: frameCount=${currentState.frameCount}, avgTime=${currentState.averageProcessingTime}")
            
            // Verify metrics
            currentState.frameCount == 10L && currentState.averageProcessingTime > 0f
        } catch (e: Exception) {
            Log.e(TAG, "Performance metrics test failed", e)
            false
        }
    }
    
    /**
     * Run all tests
     */
    fun runAllTests(context: Context): TestResults {
        Log.d(TAG, "Starting real-time detection tests...")
        
        val modelTest = testModelLoading(context)
        val stateTest = testRealTimeDetectionState()
        val performanceTest = testPerformanceMetrics()
        
        val results = TestResults(
            modelLoadingTest = modelTest,
            stateManagementTest = stateTest,
            performanceTest = performanceTest
        )
        
        Log.d(TAG, "Test results: $results")
        return results
    }
    
    /**
     * Create a test bitmap for classification testing
     */
    private fun createTestBitmap(): Bitmap {
        val bitmap = Bitmap.createBitmap(224, 224, Bitmap.Config.ARGB_8888)
        bitmap.eraseColor(Color.GREEN) // Simulate a green leaf
        return bitmap
    }
    
    /**
     * Data class to hold test results
     */
    data class TestResults(
        val modelLoadingTest: Boolean,
        val stateManagementTest: Boolean,
        val performanceTest: Boolean
    ) {
        val allTestsPassed: Boolean
            get() = modelLoadingTest && stateManagementTest && performanceTest
            
        override fun toString(): String {
            return "TestResults(modelLoading=$modelLoadingTest, stateManagement=$stateManagementTest, performance=$performanceTest, allPassed=$allTestsPassed)"
        }
    }
}
