package com.ml.tomatoscan.data

import android.graphics.Bitmap
import android.util.Log
import kotlin.math.max
import kotlin.math.min

data class SeverityAnalysis(
    val severityLevel: String,
    val severityScore: Float, // 0.0 to 1.0
    val affectedAreaPercentage: Float,
    val progressionStage: String,
    val urgencyLevel: String,
    val treatmentUrgency: String,
    val prognosis: String,
    val riskFactors: List<String>,
    val monitoringFrequency: String,
    val expectedProgression: String
)

class SeverityAssessment {
    
    companion object {
        private const val TAG = "SeverityAssessment"
        
        // Severity thresholds
        private const val MILD_THRESHOLD = 0.25f
        private const val MODERATE_THRESHOLD = 0.60f
        private const val SEVERE_THRESHOLD = 0.85f
        
        // Disease progression patterns
        private val FAST_PROGRESSING_DISEASES = setOf(
            "Late Blight", "Bacterial Spot", "Fusarium Wilt"
        )
        
        private val SLOW_PROGRESSING_DISEASES = setOf(
            "Early Blight", "Septoria Leaf Spot", "Powdery Mildew"
        )
        
        private val VIRAL_DISEASES = setOf(
            "Mosaic Virus", "Leaf Curl", "Tomato Yellow Leaf Curl Virus"
        )
    }
    
    fun assessSeverity(
        disease: String,
        confidence: Float,
        bitmap: Bitmap,
        geminiAnalysis: TomatoAnalysisResult
    ): SeverityAnalysis {
        
        try {
            Log.d(TAG, "Assessing severity for: $disease with confidence: $confidence")
            
            // Step 1: Base severity from confidence and disease type
            val baseSeverity = calculateBaseSeverity(disease, confidence)
            
            // Step 2: Image-based severity analysis
            val imageSeverity = analyzeImageSeverity(bitmap)
            
            // Step 3: Gemini-enhanced severity
            val geminiSeverity = parseGeminiSeverity(geminiAnalysis)
            
            // Step 4: Combine all severity indicators
            val combinedSeverity = combineSeverityScores(baseSeverity, imageSeverity, geminiSeverity)
            
            // Step 5: Determine progression and urgency
            val progressionStage = determineProgressionStage(disease, combinedSeverity, geminiAnalysis)
            val urgencyLevel = determineUrgencyLevel(disease, combinedSeverity, progressionStage)
            
            // Step 6: Calculate affected area
            val affectedArea = estimateAffectedArea(combinedSeverity, imageSeverity)
            
            // Step 7: Generate comprehensive assessment
            return generateSeverityAnalysis(
                disease, combinedSeverity, affectedArea, progressionStage, 
                urgencyLevel, geminiAnalysis
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in severity assessment", e)
            return getDefaultSeverityAnalysis(disease, confidence)
        }
    }
    
    private fun calculateBaseSeverity(disease: String, confidence: Float): Float {
        // Base severity calculation considering disease type and confidence
        val diseaseMultiplier = when {
            disease.equals("Healthy", ignoreCase = true) -> 0.0f
            disease in FAST_PROGRESSING_DISEASES -> 1.2f
            disease in VIRAL_DISEASES -> 1.1f
            disease in SLOW_PROGRESSING_DISEASES -> 0.9f
            disease.contains("Deficiency", ignoreCase = true) -> 0.7f
            else -> 1.0f
        }
        
        return min(1.0f, confidence * diseaseMultiplier)
    }
    
    private fun analyzeImageSeverity(bitmap: Bitmap): Float {
        try {
            // Analyze image for visual severity indicators
            val pixels = IntArray(bitmap.width * bitmap.height)
            bitmap.getPixels(pixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)
            
            var discoloredPixels = 0
            var totalPixels = pixels.size
            var avgGreenIntensity = 0f
            
            for (pixel in pixels) {
                val r = (pixel shr 16 and 0xFF) / 255f
                val g = (pixel shr 8 and 0xFF) / 255f
                val b = (pixel and 0xFF) / 255f
                
                avgGreenIntensity += g
                
                // Detect discoloration (yellowing, browning, spots)
                val isDiscolored = when {
                    // Brown/necrotic areas
                    r > 0.4f && g < 0.3f && b < 0.3f -> true
                    // Yellow areas (chlorosis)
                    r > 0.6f && g > 0.6f && b < 0.4f -> true
                    // Very dark spots
                    r < 0.2f && g < 0.2f && b < 0.2f -> true
                    // Unusual color patterns
                    (r - g) > 0.3f || (g - b) > 0.3f -> true
                    else -> false
                }
                
                if (isDiscolored) discoloredPixels++
            }
            
            avgGreenIntensity /= totalPixels
            val discolorationRatio = discoloredPixels.toFloat() / totalPixels
            
            // Healthy leaves should have high green intensity and low discoloration
            val healthScore = avgGreenIntensity * (1f - discolorationRatio)
            val severityScore = 1f - healthScore
            
            Log.d(TAG, "Image analysis - Discoloration: ${discolorationRatio * 100}%, Green intensity: ${avgGreenIntensity * 100}%, Severity: ${severityScore * 100}%")
            
            return max(0f, min(1f, severityScore))
            
        } catch (e: Exception) {
            Log.e(TAG, "Error in image severity analysis", e)
            return 0.5f // Default moderate severity
        }
    }
    
    private fun parseGeminiSeverity(geminiAnalysis: TomatoAnalysisResult): Float {
        return when (geminiAnalysis.severity.lowercase()) {
            "healthy" -> 0.0f
            "mild" -> 0.3f
            "moderate" -> 0.6f
            "severe" -> 0.9f
            else -> geminiAnalysis.confidence / 100f // Use confidence as fallback
        }
    }
    
    private fun combineSeverityScores(base: Float, image: Float, gemini: Float): Float {
        // Weighted combination with emphasis on Gemini analysis for accuracy
        val weights = floatArrayOf(0.3f, 0.3f, 0.4f) // base, image, gemini
        return (base * weights[0] + image * weights[1] + gemini * weights[2])
    }
    
    private fun determineProgressionStage(
        disease: String, 
        severity: Float, 
        geminiAnalysis: TomatoAnalysisResult
    ): String {
        
        // Use Gemini analysis if available
        if (geminiAnalysis.progressionStage != "Unknown") {
            return geminiAnalysis.progressionStage
        }
        
        // Determine based on severity and disease characteristics
        return when {
            severity < 0.2f -> "Early"
            severity < 0.5f -> "Developing"
            severity < 0.8f -> "Advanced"
            else -> "Late"
        }
    }
    
    private fun determineUrgencyLevel(disease: String, severity: Float, stage: String): String {
        return when {
            disease.equals("Healthy", ignoreCase = true) -> "None"
            disease in FAST_PROGRESSING_DISEASES && severity > 0.4f -> "Critical"
            severity > 0.8f -> "High"
            severity > 0.5f || disease in FAST_PROGRESSING_DISEASES -> "Medium"
            else -> "Low"
        }
    }
    
    private fun estimateAffectedArea(combinedSeverity: Float, imageSeverity: Float): Float {
        // Estimate percentage of leaf area affected
        val baseArea = combinedSeverity * 100f
        val imageAdjustment = (imageSeverity - 0.5f) * 20f // ±20% adjustment
        return max(0f, min(100f, baseArea + imageAdjustment))
    }
    
    private fun generateSeverityAnalysis(
        disease: String,
        severity: Float,
        affectedArea: Float,
        progressionStage: String,
        urgencyLevel: String,
        geminiAnalysis: TomatoAnalysisResult
    ): SeverityAnalysis {
        
        val severityLevel = when {
            severity < MILD_THRESHOLD -> "Mild"
            severity < MODERATE_THRESHOLD -> "Moderate"
            severity < SEVERE_THRESHOLD -> "Severe"
            else -> "Critical"
        }
        
        val treatmentUrgency = when (urgencyLevel) {
            "Critical" -> "Immediate treatment required (within 24 hours)"
            "High" -> "Urgent treatment needed (within 2-3 days)"
            "Medium" -> "Treatment recommended (within 1 week)"
            "Low" -> "Monitor and treat as needed"
            else -> "No immediate treatment required"
        }
        
        val prognosis = generatePrognosis(disease, severity, progressionStage)
        val riskFactors = identifyRiskFactors(disease, geminiAnalysis)
        val monitoringFrequency = determineMonitoringFrequency(urgencyLevel, disease)
        val expectedProgression = predictProgression(disease, severity, progressionStage)
        
        return SeverityAnalysis(
            severityLevel = severityLevel,
            severityScore = severity,
            affectedAreaPercentage = affectedArea,
            progressionStage = progressionStage,
            urgencyLevel = urgencyLevel,
            treatmentUrgency = treatmentUrgency,
            prognosis = prognosis,
            riskFactors = riskFactors,
            monitoringFrequency = monitoringFrequency,
            expectedProgression = expectedProgression
        )
    }
    
    private fun generatePrognosis(disease: String, severity: Float, stage: String): String {
        return when {
            disease.equals("Healthy", ignoreCase = true) -> "Excellent - plant is healthy"
            severity < 0.3f && stage == "Early" -> "Good - early intervention should be effective"
            severity < 0.6f -> "Fair - treatment needed but recovery likely with proper care"
            severity < 0.8f -> "Guarded - aggressive treatment required, partial recovery possible"
            else -> "Poor - severe damage, focus on preventing spread to other plants"
        }
    }
    
    private fun identifyRiskFactors(disease: String, geminiAnalysis: TomatoAnalysisResult): List<String> {
        val factors = mutableListOf<String>()
        
        // Add environmental factors from Gemini
        factors.addAll(geminiAnalysis.environmentalFactors)
        
        // Add disease-specific risk factors
        when {
            disease.contains("Blight", ignoreCase = true) -> {
                factors.addAll(listOf("High humidity", "Poor air circulation", "Overhead watering"))
            }
            disease.contains("Bacterial", ignoreCase = true) -> {
                factors.addAll(listOf("Wet conditions", "Plant wounds", "Contaminated tools"))
            }
            disease.contains("Virus", ignoreCase = true) -> {
                factors.addAll(listOf("Insect vectors", "Contaminated tools", "Infected plant material"))
            }
        }
        
        return factors.distinct()
    }
    
    private fun determineMonitoringFrequency(urgencyLevel: String, disease: String): String {
        return when (urgencyLevel) {
            "Critical" -> "Daily monitoring required"
            "High" -> "Monitor every 2-3 days"
            "Medium" -> "Weekly monitoring recommended"
            "Low" -> "Monitor every 2 weeks"
            else -> "Monthly check sufficient"
        }
    }
    
    private fun predictProgression(disease: String, severity: Float, stage: String): String {
        val timeframe = when {
            disease in FAST_PROGRESSING_DISEASES -> "rapid (days to weeks)"
            disease in VIRAL_DISEASES -> "variable (weeks to months)"
            else -> "gradual (weeks to months)"
        }
        
        return "Expected progression: $timeframe without treatment"
    }
    
    private fun getDefaultSeverityAnalysis(disease: String, confidence: Float): SeverityAnalysis {
        return SeverityAnalysis(
            severityLevel = "Unknown",
            severityScore = confidence,
            affectedAreaPercentage = 0f,
            progressionStage = "Unknown",
            urgencyLevel = "Low",
            treatmentUrgency = "Assessment needed",
            prognosis = "Unable to determine",
            riskFactors = listOf("Analysis incomplete"),
            monitoringFrequency = "Regular monitoring recommended",
            expectedProgression = "Unknown"
        )
    }
}
