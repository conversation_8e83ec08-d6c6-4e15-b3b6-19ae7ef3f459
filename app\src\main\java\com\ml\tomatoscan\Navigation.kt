package com.ml.tomatoscan

import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.Composable
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.ml.tomatoscan.ui.screens.MainScreen
import com.ml.tomatoscan.ui.screens.SplashScreen
import com.ml.tomatoscan.viewmodels.LanguageViewModel
import com.ml.tomatoscan.viewmodels.ThemeViewModel

@OptIn(ExperimentalAnimationApi::class)
@Composable
fun Navigation(themeViewModel: ThemeViewModel, languageViewModel: LanguageViewModel) {
    val navController = rememberNavController()

    NavHost(navController = navController, startDestination = "splash") {
        composable("splash") {
            SplashScreen(navController = navController)
        }
        composable(
            "dashboard",
            enterTransition = { fadeIn(animationSpec = tween(300)) },
            popExitTransition = { fadeOut(animationSpec = tween(300)) },
        ) {
            MainScreen(themeViewModel = themeViewModel, languageViewModel = languageViewModel)
        }
    }
}
