package com.ml.tomatoscan.ui.screens

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.ImageDecoder
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import android.speech.tts.TextToSpeech
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Replay
import androidx.compose.material.icons.filled.Science
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import kotlinx.coroutines.delay
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.ml.tomatoscan.ui.screens.analysis.ActionButtons
import com.ml.tomatoscan.ui.screens.analysis.AnalysisContent
import com.ml.tomatoscan.ui.screens.analysis.CameraPreview
import com.ml.tomatoscan.ui.screens.camera.RealTimeCameraDetection
import com.ml.tomatoscan.ui.components.DetectionModeSelector
import com.ml.tomatoscan.ui.components.RealTimeDetectionStatusCard
import com.ml.tomatoscan.ui.components.DetectionInstructions
import com.ml.tomatoscan.data.DetectionMode
import com.ml.tomatoscan.data.RealTimeDetectionState
import com.ml.tomatoscan.viewmodels.TomatoScanViewModel
import java.util.Locale

private val UriSaver = Saver<Uri?, String>(
    save = { it?.toString() ?: "" },
    restore = { if (it.isNotEmpty()) Uri.parse(it) else null }
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AnalysisScreen(
    viewModel: TomatoScanViewModel
) {
    val context = LocalContext.current
    val scanResult by viewModel.scanResult.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val imageUri by viewModel.analysisImageUri.collectAsState()
    val directCameraMode by viewModel.directCameraMode.collectAsState()
    val detectionMode by viewModel.detectionMode.collectAsState()
    val realTimeDetectionState by viewModel.realTimeDetectionState.collectAsState()

    // Simplified state management
    var showCameraPreview by rememberSaveable { mutableStateOf(false) }
    var imageFromCamera by rememberSaveable { mutableStateOf(false) }
    var pendingRealTimeMode by remember { mutableStateOf(false) }

    val textToSpeech = remember(context) {
        var tts: TextToSpeech? = null
        tts = TextToSpeech(context) { status ->
            if (status == TextToSpeech.SUCCESS) {
                tts?.language = Locale.US
            }
        }
        tts
    }

    DisposableEffect(Unit) {
        onDispose {
            textToSpeech?.stop()
            textToSpeech?.shutdown()
        }
    }

    LaunchedEffect(scanResult) {
        scanResult?.let {
            val textToSpeak = "Analysis complete. Quality is ${it.quality} with ${it.confidence} percent confidence."
            textToSpeech?.speak(textToSpeak, TextToSpeech.QUEUE_FLUSH, null, null)
        }
    }

    // Improved camera permission handling
    val cameraPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            when {
                pendingRealTimeMode -> {
                    viewModel.switchToRealTimeMode()
                    pendingRealTimeMode = false
                }
                detectionMode == DetectionMode.IMAGE_UPLOAD -> {
                    showCameraPreview = true
                }
            }
        } else {
            Log.e("AnalysisScreen", "Camera permission denied.")
            pendingRealTimeMode = false
        }
    }

    // Auto-trigger camera when in direct camera mode
    LaunchedEffect(directCameraMode) {
        if (directCameraMode) {
            when (PackageManager.PERMISSION_GRANTED) {
                ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA) -> {
                    showCameraPreview = true
                }
                else -> {
                    cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                }
            }
            viewModel.setDirectCameraMode(false) // Reset the flag
        }
    }

    val galleryLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent()
    ) { uri: Uri? ->
        uri?.let {
            viewModel.setAnalysisImageUri(it)
            imageFromCamera = false
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Tomato Analysis") },
                navigationIcon = {
                    if (scanResult != null || showCameraPreview || imageUri != null) {
                        IconButton(onClick = {
                            when {
                                showCameraPreview -> showCameraPreview = false
                                imageUri != null -> viewModel.setAnalysisImageUri(null)
                                else -> viewModel.clearAnalysisState()
                            }
                        }) {
                            Icon(Icons.Filled.ArrowBack, contentDescription = "Back")
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            when {
                // Real-time detection mode
                detectionMode == DetectionMode.REAL_TIME_CAMERA -> {
                    Log.d("AnalysisScreen", "Displaying RealTimeCameraDetection component")
                    RealTimeCameraDetection(
                        detectionState = realTimeDetectionState,
                        onDetectionResult = { result ->
                            Log.d("AnalysisScreen", "Real-time result: ${result.disease} (${result.confidence})")
                        },
                        onCaptureImage = {
                            // Switch to upload mode and capture image
                            viewModel.switchToImageUploadMode()
                            showCameraPreview = true
                        },
                        onClose = {
                            viewModel.switchToImageUploadMode()
                        },
                        onFrameProcessing = { bitmap ->
                            viewModel.processRealTimeFrame(bitmap)
                        }
                    )
                }

                // Camera preview for image upload
                showCameraPreview -> {
                    CameraPreview(
                        onImageCaptured = { uri ->
                            showCameraPreview = false
                            viewModel.setAnalysisImageUri(uri)
                            imageFromCamera = true
                        },
                        onError = { Log.e("AnalysisScreen", "Image capture error: $it") },
                        onClose = { showCameraPreview = false }
                    )
                }

                // Analysis states for image upload mode
                isLoading -> {
                    imageUri?.let { uri ->
                        AnalysisInProgressScreen(uri = uri)
                    }
                }

                scanResult != null -> {
                    imageUri?.let { uri ->
                        AnalysisContent(
                            viewModel = viewModel,
                            imageUri = uri,
                            onAnalyzeAnother = {
                                viewModel.clearAnalysisState()
                            }
                        )
                    }
                }

                else -> {
                    imageUri?.let { uri ->
                        ImagePreview(
                            uri = uri,
                            fromCamera = imageFromCamera,
                            onAnalyze = {
                                val bitmap = if (Build.VERSION.SDK_INT < 28) {
                                    @Suppress("DEPRECATION")
                                    MediaStore.Images.Media.getBitmap(context.contentResolver, uri)
                                } else {
                                    val source = ImageDecoder.createSource(context.contentResolver, uri)
                                    ImageDecoder.decodeBitmap(source)
                                }
                                viewModel.analyzeImage(bitmap, uri)
                            },
                            onRetake = {
                                viewModel.setAnalysisImageUri(null)
                                if (imageFromCamera) {
                                    showCameraPreview = true
                                } else {
                                    galleryLauncher.launch("image/*")
                                }
                            }
                        )
                    } ?: run {
                        // Show main selection screen when no image is selected
                        MainSelectionScreen(
                            detectionMode = detectionMode,
                            realTimeDetectionState = realTimeDetectionState,
                            onModeSelected = { mode ->
                                when (mode) {
                                    DetectionMode.REAL_TIME_CAMERA -> {
                                        Log.d("AnalysisScreen", "Real-time mode selected")
                                        when (PackageManager.PERMISSION_GRANTED) {
                                            ContextCompat.checkSelfPermission(
                                                context,
                                                Manifest.permission.CAMERA
                                            ) -> {
                                                Log.d("AnalysisScreen", "Camera permission granted, switching to real-time mode")
                                                viewModel.switchToRealTimeMode()
                                            }
                                            else -> {
                                                Log.d("AnalysisScreen", "Camera permission not granted, requesting permission")
                                                pendingRealTimeMode = true
                                                cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                                            }
                                        }
                                    }
                                    DetectionMode.IMAGE_UPLOAD -> {
                                        viewModel.switchToImageUploadMode()
                                    }
                                    else -> {}
                                }
                            },
                        onCaptureClick = {
                            when (PackageManager.PERMISSION_GRANTED) {
                                ContextCompat.checkSelfPermission(
                                    context,
                                    Manifest.permission.CAMERA
                                ) -> {
                                    showCameraPreview = true
                                }
                                else -> {
                                    cameraPermissionLauncher.launch(Manifest.permission.CAMERA)
                                }
                            }
                        },
                        onUploadClick = { galleryLauncher.launch("image/*") }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun MainSelectionScreen(
    detectionMode: DetectionMode,
    realTimeDetectionState: RealTimeDetectionState,
    onModeSelected: (DetectionMode) -> Unit,
    onCaptureClick: () -> Unit,
    onUploadClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Header section
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Text(
                text = "Choose Detection Method",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onBackground,
                textAlign = TextAlign.Center
            )
            Text(
                text = "Select how you want to analyze tomato leaves",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Detection Mode Selector - More prominent
        DetectionModeSelector(
            currentMode = detectionMode,
            onModeSelected = onModeSelected,
            modifier = Modifier.fillMaxWidth()
        )

        // Instructions based on selected mode
        DetectionInstructions(
            isRealTimeMode = detectionMode == DetectionMode.REAL_TIME_CAMERA,
            modifier = Modifier.fillMaxWidth()
        )

        // Real-time detection status (only show if real-time mode is selected)
        if (detectionMode == DetectionMode.REAL_TIME_CAMERA) {
            RealTimeDetectionStatusCard(
                detectionState = realTimeDetectionState,
                modifier = Modifier.fillMaxWidth()
            )
        }

        Spacer(modifier = Modifier.weight(1f))

        // Action buttons for image upload mode only
        if (detectionMode == DetectionMode.IMAGE_UPLOAD) {
            ActionButtons(
                onCaptureClick = onCaptureClick,
                onUploadClick = onUploadClick
            )
        }
    }
}

@Composable
private fun ImagePreview(
    uri: Uri,
    fromCamera: Boolean,
    onAnalyze: () -> Unit,
    onRetake: () -> Unit
) {
    val context = LocalContext.current
    val bitmap = remember(uri) {
        if (Build.VERSION.SDK_INT < 28) {
            @Suppress("DEPRECATION")
            MediaStore.Images.Media.getBitmap(context.contentResolver, uri)
        } else {
            val source = ImageDecoder.createSource(context.contentResolver, uri)
            ImageDecoder.decodeBitmap(source)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.surface)
    ) {
        // Image with rounded corners and shadow
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "Captured Image Preview",
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        MaterialTheme.colorScheme.surfaceVariant,
                        RoundedCornerShape(16.dp)
                    ),
                contentScale = ContentScale.Fit
            )
        }

        // Action buttons with improved design
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.BottomCenter)
                .background(
                    Brush.verticalGradient(
                        colors = listOf(
                            Color.Transparent,
                            MaterialTheme.colorScheme.surface.copy(alpha = 0.95f)
                        )
                    )
                )
                .padding(24.dp),
            horizontalArrangement = Arrangement.spacedBy(16.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            FilledTonalButton(
                onClick = onRetake,
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .height(56.dp)
                    .weight(1f)
            ) {
                Icon(Icons.Default.Replay, contentDescription = "Retake")
                Spacer(modifier = Modifier.width(8.dp))
                Text(if (fromCamera) "Retake" else "Choose Another")
            }
            Button(
                onClick = onAnalyze,
                shape = RoundedCornerShape(16.dp),
                modifier = Modifier
                    .height(56.dp)
                    .weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(Icons.Default.Science, contentDescription = "Analyze")
                Spacer(modifier = Modifier.width(8.dp))
                Text("Analyze")
            }
        }
    }
}

@Composable
private fun AnalysisInProgressScreen(uri: Uri) {
    val context = LocalContext.current
    val bitmap = remember(uri) {
        if (Build.VERSION.SDK_INT < 28) {
            @Suppress("DEPRECATION")
            MediaStore.Images.Media.getBitmap(context.contentResolver, uri)
        } else {
            val source = ImageDecoder.createSource(context.contentResolver, uri)
            ImageDecoder.decodeBitmap(source)
        }
    }

    val infiniteTransition = rememberInfiniteTransition(label = "scanner")
    val scanPosition by infiniteTransition.animateFloat(
        initialValue = -0.1f, // Start off-screen
        targetValue = 1.1f, // End off-screen
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "scan_position"
    )
    val primaryColor = MaterialTheme.colorScheme.primary
    val surfaceColor = MaterialTheme.colorScheme.surface
    val surfaceVariantColor = MaterialTheme.colorScheme.surfaceVariant

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(surfaceColor)
    ) {
        // Image with rounded corners
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "Analyzing Image",
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        surfaceVariantColor,
                        RoundedCornerShape(16.dp)
                    ),
                contentScale = ContentScale.Crop
            )
        }

        // Scanning overlay with improved design
        Canvas(modifier = Modifier.fillMaxSize().padding(16.dp)) {
            val canvasHeight = size.height
            val canvasWidth = size.width
            val lineY = canvasHeight * scanPosition

            // Semi-transparent overlay
            drawRect(
                color = surfaceColor.copy(alpha = 0.7f),
                topLeft = Offset.Zero,
                size = size
            )

            // Glowing scanner line
            val scannerBrush = Brush.verticalGradient(
                colors = listOf(
                    primaryColor.copy(alpha = 0f),
                    primaryColor.copy(alpha = 0.9f),
                    primaryColor,
                    primaryColor.copy(alpha = 0.9f),
                    primaryColor.copy(alpha = 0f)
                ),
                startY = lineY - 30.dp.toPx(),
                endY = lineY + 30.dp.toPx()
            )
            drawLine(
                brush = scannerBrush,
                start = Offset(0f, lineY),
                end = Offset(canvasWidth, lineY),
                strokeWidth = 6.dp.toPx(),
                cap = StrokeCap.Round
            )
        }

        // Progress indicator and text
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(32.dp),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(72.dp),
                color = MaterialTheme.colorScheme.primary,
                strokeWidth = 6.dp
            )
            Spacer(modifier = Modifier.height(32.dp))
            Text(
                "Analyzing Tomato Leaf...",
                style = MaterialTheme.typography.headlineSmall,
                color = MaterialTheme.colorScheme.onSurface,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                "Please wait while we detect diseases",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}