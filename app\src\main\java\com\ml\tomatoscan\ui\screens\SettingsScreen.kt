package com.ml.tomatoscan.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import com.ml.tomatoscan.R
import com.ml.tomatoscan.ui.theme.Theme
import com.ml.tomatoscan.viewmodels.Language
import com.ml.tomatoscan.viewmodels.LanguageViewModel
import com.ml.tomatoscan.viewmodels.ThemeViewModel
import com.ml.tomatoscan.viewmodels.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    userViewModel: UserViewModel,
    themeViewModel: ThemeViewModel,
    languageViewModel: LanguageViewModel
) {
    var showAboutDialog by remember { mutableStateOf(false) }
    var showThemeDialog by remember { mutableStateOf(false) }
    var showNameDialog by remember { mutableStateOf(false) }
    var showLanguageDialog by remember { mutableStateOf(false) }
    val userName by userViewModel.userName.collectAsState()
    val currentTheme by themeViewModel.theme.collectAsState()
    val currentLanguage by languageViewModel.language.collectAsState()

    val backgroundGradient = Brush.verticalGradient(
        colors = listOf(
            MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
            MaterialTheme.colorScheme.surface
        )
    )

    Scaffold(
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text(stringResource(R.string.settings), fontWeight = FontWeight.Bold) },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.Default.ArrowBack, contentDescription = stringResource(R.string.back))
                    }
                },
                colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(backgroundGradient)
                .padding(innerPadding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(24.dp)
            ) {
                // Account Section
                SettingsSection(
                    title = stringResource(R.string.account),
                    items = listOf(
                        SettingsItem(
                            icon = Icons.Default.Person,
                            title = stringResource(R.string.change_name),
                            subtitle = stringResource(R.string.current_name, userName),
                            onClick = { showNameDialog = true }
                        )
                    )
                )

                // App Section
                SettingsSection(
                    title = stringResource(R.string.application),
                    items = listOf(
                        SettingsItem(
                            icon = Icons.Default.Palette,
                            title = stringResource(R.string.theme),
                            subtitle = "${stringResource(R.string.customize_app_appearance)}\n${stringResource(R.string.current_theme, currentTheme.name.lowercase().replaceFirstChar { it.uppercase() })}",
                            onClick = { showThemeDialog = true }
                        ),
                        SettingsItem(
                            icon = Icons.Default.Notifications,
                            title = stringResource(R.string.notifications),
                            subtitle = stringResource(R.string.manage_notification_preferences),
                            onClick = { /* TODO */ }
                        ),
                        SettingsItem(
                            icon = Icons.Default.Language,
                            title = stringResource(R.string.language),
                            subtitle = "${stringResource(R.string.choose_your_preferred_language)}\n${stringResource(R.string.current_language, currentLanguage.name.lowercase().replaceFirstChar { it.uppercase() })}",
                            onClick = { showLanguageDialog = true }
                        )
                    )
                )

                // Data Section
                SettingsSection(
                    title = stringResource(R.string.data_privacy),
                    items = listOf(
                        SettingsItem(
                            icon = Icons.Default.Security,
                            title = stringResource(R.string.privacy_policy),
                            subtitle = stringResource(R.string.view_our_privacy_policy),
                            onClick = { /* TODO */ }
                        ),
                        SettingsItem(
                            icon = Icons.Default.DeleteSweep,
                            title = stringResource(R.string.clear_data),
                            subtitle = stringResource(R.string.remove_all_local_data),
                            onClick = { /* TODO */ },
                            textColor = MaterialTheme.colorScheme.error,
                            hasNavigation = false
                        )
                    )
                )

                // About Section
                SettingsSection(
                    title = stringResource(R.string.about),
                    items = listOf(
                        SettingsItem(
                            icon = Icons.Default.Info,
                            title = stringResource(R.string.about_tomatoscan),
                            subtitle = stringResource(R.string.version),
                            onClick = { showAboutDialog = true }
                        ),
                        SettingsItem(
                            icon = Icons.Default.Star,
                            title = stringResource(R.string.rate_app),
                            subtitle = stringResource(R.string.rate_us_on_play_store),
                            onClick = { /* TODO */ }
                        )
                    )
                )
            }
        }
    }

    if (showAboutDialog) {
        AboutDialog(onDismiss = { showAboutDialog = false })
    }

    if (showThemeDialog) {
        ThemeDialog(
            onDismiss = { showThemeDialog = false },
            onThemeSelected = { themeViewModel.setTheme(it) },
            currentTheme = currentTheme
        )
    }

    if (showLanguageDialog) {
        LanguageDialog(
            onDismiss = { showLanguageDialog = false },
            onLanguageSelected = { languageViewModel.setLanguage(it) },
            currentLanguage = currentLanguage
        )
    }

    if (showNameDialog) {
        NameChangeDialog(
            currentName = userName,
            onDismiss = { showNameDialog = false },
            onConfirm = { newName ->
                userViewModel.updateUserName(newName)
                showNameDialog = false
            }
        )
    }
}

@Composable
fun SettingsSection(title: String, items: List<SettingsItem>) {
    Column {
        Text(
            text = title,
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.primary,
            modifier = Modifier.padding(start = 16.dp, bottom = 8.dp)
        )
        Card(
            modifier = Modifier.fillMaxWidth(),
            shape = RoundedCornerShape(16.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
        ) {
            Column {
                items.forEachIndexed { index, item ->
                    SettingsItemRow(item = item)
                    if (index < items.size - 1) {
                        Divider(
                            modifier = Modifier.padding(horizontal = 16.dp),
                            color = MaterialTheme.colorScheme.outline.copy(alpha = 0.1f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun SettingsItemRow(item: SettingsItem) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { item.onClick() }
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(40.dp)
                .clip(CircleShape)
                .background(item.textColor?.copy(alpha = 0.1f) ?: MaterialTheme.colorScheme.primary.copy(alpha = 0.1f)),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = item.icon,
                contentDescription = item.title,
                tint = item.textColor ?: MaterialTheme.colorScheme.primary,
                modifier = Modifier.size(22.dp)
            )
        }

        Spacer(modifier = Modifier.width(16.dp))

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = item.title,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                color = item.textColor ?: MaterialTheme.colorScheme.onSurface
            )
            if (item.subtitle.isNotEmpty()) {
                Text(
                    text = item.subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }

        if (item.hasNavigation) {
            Icon(
                imageVector = Icons.Default.ChevronRight,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
                modifier = Modifier.size(24.dp)
            )
        }
    }
}

@Composable
fun AboutDialog(onDismiss: () -> Unit) {
    AlertDialog(
        onDismissRequest = onDismiss,
        icon = { Icon(Icons.Default.Info, contentDescription = stringResource(R.string.about)) },
        title = { Text(stringResource(R.string.about_tomatoscan), fontWeight = FontWeight.Bold) },
        text = {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                Text(stringResource(R.string.version), style = MaterialTheme.typography.labelLarge)
                Text(
                    stringResource(R.string.about_dialog_desc),
                    style = MaterialTheme.typography.bodyMedium
                )
                Text(
                    stringResource(R.string.powered_by_gemini),
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) { Text(stringResource(R.string.close)) }
        }
    )
}

@Composable
fun ThemeDialog(onDismiss: () -> Unit, onThemeSelected: (Theme) -> Unit, currentTheme: Theme) {
    val themes = listOf(Theme.SYSTEM, Theme.LIGHT, Theme.DARK)
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(stringResource(R.string.choose_theme)) },
        text = {
            Column {
                themes.forEach { theme ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onThemeSelected(theme); onDismiss() }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentTheme == theme,
                            onClick = { onThemeSelected(theme); onDismiss() }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            when (theme) {
                                Theme.SYSTEM -> stringResource(R.string.system_default)
                                Theme.LIGHT -> stringResource(R.string.light)
                                Theme.DARK -> stringResource(R.string.dark)
                            }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.close))
            }
        }
    )
}

@Composable
fun LanguageDialog(onDismiss: () -> Unit, onLanguageSelected: (Language) -> Unit, currentLanguage: Language) {
    val languages = listOf(Language.ENGLISH, Language.FILIPINO, Language.BISAYA)
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text(stringResource(R.string.choose_language)) },
        text = {
            Column {
                languages.forEach { language ->
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .clickable { onLanguageSelected(language); onDismiss() }
                            .padding(vertical = 12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        RadioButton(
                            selected = currentLanguage == language,
                            onClick = { onLanguageSelected(language); onDismiss() }
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            when (language) {
                                Language.ENGLISH -> stringResource(R.string.language_english)
                                Language.FILIPINO -> stringResource(R.string.language_filipino)
                                Language.BISAYA -> stringResource(R.string.language_bisaya)
                            }
                        )
                    }
                }
            }
        },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.close))
            }
        }
    )
}

@Composable
fun NameChangeDialog(currentName: String, onDismiss: () -> Unit, onConfirm: (String) -> Unit) {
    var newName by remember { mutableStateOf(currentName) }

    AlertDialog(
        onDismissRequest = onDismiss,
        icon = { Icon(Icons.Default.Person, contentDescription = stringResource(R.string.change_name)) },
        title = { Text(stringResource(R.string.change_your_name), fontWeight = FontWeight.Bold) },
        text = {
            OutlinedTextField(
                value = newName,
                onValueChange = { newName = it },
                label = { Text(stringResource(R.string.name)) },
                singleLine = true,
                modifier = Modifier.fillMaxWidth()
            )
        },
        confirmButton = {
            Button(
                onClick = { onConfirm(newName) },
                enabled = newName.isNotBlank() && newName != currentName
            ) {
                Text(stringResource(R.string.save))
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text(stringResource(R.string.cancel))
            }
        }
    )
}

data class SettingsItem(
    val icon: ImageVector,
    val title: String,
    val subtitle: String,
    val onClick: () -> Unit,
    val textColor: Color? = null,
    val hasNavigation: Boolean = true
)
//data class SettingsItem(
//    val icon: ImageVector,
//    val title: String,
//    val subtitle: String,
//    val onClick: () -> Unit,
//    val textColor: Color? = null,
//    val hasNavigation: Boolean = true
//)