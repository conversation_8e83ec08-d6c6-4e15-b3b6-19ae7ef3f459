package com.ml.tomatoscan.data

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.ColorMatrix
import android.graphics.ColorMatrixColorFilter
import android.graphics.Paint
import android.util.Log
import org.tensorflow.lite.support.tensorbuffer.TensorBuffer
import org.tensorflow.lite.DataType
import com.ml.tomatoscan.ml.BestInt8
import androidx.core.graphics.scale
import kotlin.math.max
import kotlin.math.min
import java.io.IOException

data class ClassificationResult(
    val disease: String,
    val confidence: Float,
    val allConfidences: FloatArray,
    val isHighConfidence: Boolean,
    val preprocessingQuality: Float
)

class TomatoTFLiteClassifier(private val context: Context) {
    private var model: BestInt8? = null
    private val inputImageWidth = 224
    private val inputImageHeight = 224
    private val inputImageChannels = 3
    private val numClasses = 10 // Expanded for more disease types

    // Expanded disease labels for better coverage
    private val labels = listOf(
        "Healthy",
        "Early Blight",
        "Late Blight",
        "Septoria Leaf Spot",
        "Bacterial Spot",
        "Mosaic Virus",
        "Powdery Mildew",
        "Fusarium Wilt",
        "Leaf Curl",
        "Nutrient Deficiency"
    )

    // Confidence thresholds for different quality assessments
    private val highConfidenceThreshold = 0.85f
    private val mediumConfidenceThreshold = 0.65f
    private val lowConfidenceThreshold = 0.45f

    init {
        loadModel()
    }

    private fun loadModel() {
        try {
            Log.d("TomatoTFLiteClassifier", "Starting model loading using ML Model Binding...")
            Log.d("TomatoTFLiteClassifier", "Context: ${context.javaClass.simpleName}")

            // Verify model file exists
            val modelPath = "best_int8.tflite"
            Log.d("TomatoTFLiteClassifier", "Looking for model file: $modelPath")

            // Use the generated model class from ML Model Binding
            model = BestInt8.newInstance(context)

            if (model != null) {
                Log.d("TomatoTFLiteClassifier", "Model loaded successfully using ML Model Binding")
                Log.d("TomatoTFLiteClassifier", "Model ready status: ${isModelReady()}")
                Log.d("TomatoTFLiteClassifier", "Available disease labels: ${labels.size}")
                Log.d("TomatoTFLiteClassifier", "Disease labels: ${labels.joinToString(", ")}")

                // Test model with dummy input to verify it's working
                testModelInitialization()
            } else {
                Log.e("TomatoTFLiteClassifier", "Model instance is null after loading")
            }
        } catch (e: IOException) {
            Log.e("TomatoTFLiteClassifier", "IO Exception during model loading - model file may be missing", e)
        } catch (e: IllegalArgumentException) {
            Log.e("TomatoTFLiteClassifier", "Invalid model format or corrupted model file", e)
        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Unexpected error during model loading", e)
            Log.e("TomatoTFLiteClassifier", "Exception details: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun testModelInitialization() {
        try {
            Log.d("TomatoTFLiteClassifier", "Testing model initialization with dummy input...")
            val testBitmap = Bitmap.createBitmap(inputImageWidth, inputImageHeight, Bitmap.Config.RGB_565)
            val testBuffer = convertBitmapToTensorBuffer(testBitmap)
            val outputs = model?.process(testBuffer)
            val confidences = outputs?.getOutputFeature0AsTensorBuffer()?.floatArray

            if (confidences != null && confidences.size == labels.size) {
                Log.d("TomatoTFLiteClassifier", "Model initialization test successful - output size: ${confidences.size}")
            } else {
                Log.w("TomatoTFLiteClassifier", "Model test failed - unexpected output size: ${confidences?.size}")
            }
        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Model initialization test failed", e)
        }
    }



    fun classify(bitmap: Bitmap): Pair<String, Float> {
        val result = classifyAdvanced(bitmap)
        return result.disease to result.confidence
    }

    fun classifyAdvanced(bitmap: Bitmap): ClassificationResult {
        try {
            // Step 1: Validate and preprocess image
            val preprocessingQuality = assessImageQuality(bitmap)
            if (preprocessingQuality < 0.3f) {
                Log.w("TomatoTFLiteClassifier", "Low image quality detected: $preprocessingQuality")
            }

            // Step 2: Enhanced preprocessing
            val preprocessedBitmap = enhancedPreprocessing(bitmap)
            val inputBuffer = convertBitmapToTensorBuffer(preprocessedBitmap)

            // Step 3: Run inference
            val outputs = model?.process(inputBuffer)
            val confidences = outputs?.getOutputFeature0AsTensorBuffer()?.floatArray ?: FloatArray(labels.size)

            // Step 4: Process results with confidence analysis
            val maxIdx = confidences.indices.maxByOrNull { confidences[it] } ?: -1
            val maxConfidence = if (maxIdx >= 0) confidences[maxIdx] else 0f

            // Step 5: Apply confidence-based filtering
            val finalDisease = when {
                maxIdx < 0 || maxIdx >= labels.size -> "Unknown"
                maxConfidence < lowConfidenceThreshold -> "Uncertain"
                else -> labels[maxIdx]
            }

            val isHighConfidence = maxConfidence >= highConfidenceThreshold

            Log.d("TomatoTFLiteClassifier", "Classification: $finalDisease (${maxConfidence * 100}%), High confidence: $isHighConfidence")

            return ClassificationResult(
                disease = finalDisease,
                confidence = maxConfidence,
                allConfidences = confidences,
                isHighConfidence = isHighConfidence,
                preprocessingQuality = preprocessingQuality
            )

        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Classification failed", e)
            return ClassificationResult(
                disease = "Error",
                confidence = 0f,
                allConfidences = FloatArray(labels.size),
                isHighConfidence = false,
                preprocessingQuality = 0f
            )
        }
    }

    private fun assessImageQuality(bitmap: Bitmap): Float {
        try {
            // Check resolution
            val resolutionScore = min(1.0f, (bitmap.width * bitmap.height) / (224f * 224f))

            // Check if image is too dark or too bright
            val pixels = IntArray(bitmap.width * bitmap.height)
            bitmap.getPixels(pixels, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)

            var totalBrightness = 0f
            var variance = 0f

            // Calculate average brightness
            for (pixel in pixels) {
                val r = (pixel shr 16 and 0xFF) / 255f
                val g = (pixel shr 8 and 0xFF) / 255f
                val b = (pixel and 0xFF) / 255f
                val brightness = (r + g + b) / 3f
                totalBrightness += brightness
            }

            val avgBrightness = totalBrightness / pixels.size

            // Calculate variance for contrast assessment
            for (pixel in pixels) {
                val r = (pixel shr 16 and 0xFF) / 255f
                val g = (pixel shr 8 and 0xFF) / 255f
                val b = (pixel and 0xFF) / 255f
                val brightness = (r + g + b) / 3f
                variance += (brightness - avgBrightness) * (brightness - avgBrightness)
            }
            variance /= pixels.size

            // Score based on optimal brightness (0.3-0.7) and good contrast
            val brightnessScore = when {
                avgBrightness < 0.1f || avgBrightness > 0.9f -> 0.2f
                avgBrightness < 0.2f || avgBrightness > 0.8f -> 0.5f
                else -> 1.0f
            }

            val contrastScore = min(1.0f, variance * 10f) // Higher variance = better contrast

            return (resolutionScore + brightnessScore + contrastScore) / 3f

        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Error assessing image quality", e)
            return 0.5f // Default moderate quality
        }
    }

    private fun enhancedPreprocessing(bitmap: Bitmap): Bitmap {
        try {
            // Step 1: Resize with high quality
            val resized = Bitmap.createScaledBitmap(bitmap, inputImageWidth, inputImageHeight, true)

            // Step 2: Apply contrast enhancement for better feature detection
            val enhanced = Bitmap.createBitmap(inputImageWidth, inputImageHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(enhanced)
            val paint = Paint()

            // Enhance contrast and saturation for better leaf feature detection
            val colorMatrix = ColorMatrix().apply {
                setSaturation(1.2f) // Slightly increase saturation
                set(floatArrayOf(
                    1.1f, 0f, 0f, 0f, 0f,     // Red channel - slight boost
                    0f, 1.2f, 0f, 0f, 0f,     // Green channel - boost for leaf features
                    0f, 0f, 1.0f, 0f, 0f,     // Blue channel - normal
                    0f, 0f, 0f, 1f, 0f        // Alpha channel
                ))
            }

            paint.colorFilter = ColorMatrixColorFilter(colorMatrix)
            canvas.drawBitmap(resized, 0f, 0f, paint)

            return enhanced

        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Error in enhanced preprocessing", e)
            return bitmap.scale(inputImageWidth, inputImageHeight)
        }
    }

    private fun convertBitmapToTensorBuffer(bitmap: Bitmap): TensorBuffer {
        val tensorBuffer = TensorBuffer.createFixedSize(
            intArrayOf(1, inputImageHeight, inputImageWidth, inputImageChannels),
            DataType.FLOAT32
        )

        val intValues = IntArray(inputImageWidth * inputImageHeight)
        bitmap.getPixels(intValues, 0, bitmap.width, 0, 0, bitmap.width, bitmap.height)

        val floatValues = FloatArray(inputImageWidth * inputImageHeight * inputImageChannels)
        var pixelIndex = 0
        for (i in 0 until inputImageHeight) {
            for (j in 0 until inputImageWidth) {
                val pixelValue = intValues[pixelIndex]
                val baseIndex = pixelIndex * inputImageChannels

                // Enhanced normalization with mean subtraction (ImageNet standards)
                floatValues[baseIndex] = ((pixelValue shr 16 and 0xFF) / 255.0f - 0.485f) / 0.229f     // R
                floatValues[baseIndex + 1] = ((pixelValue shr 8 and 0xFF) / 255.0f - 0.456f) / 0.224f // G
                floatValues[baseIndex + 2] = ((pixelValue and 0xFF) / 255.0f - 0.406f) / 0.225f        // B

                pixelIndex++
            }
        }

        tensorBuffer.loadArray(floatValues)
        return tensorBuffer
    }

    fun getConfidenceLevel(confidence: Float): String {
        return when {
            confidence >= highConfidenceThreshold -> "High"
            confidence >= mediumConfidenceThreshold -> "Medium"
            confidence >= lowConfidenceThreshold -> "Low"
            else -> "Very Low"
        }
    }

    fun getAllDiseaseLabels(): List<String> = labels.toList()

    /**
     * Optimized classification method for real-time detection
     * Performs faster inference with reduced preprocessing for continuous frame analysis
     */
    fun classifyRealTime(bitmap: Bitmap): ClassificationResult {
        Log.d("TomatoTFLiteClassifier", "Starting real-time classification...")
        Log.d("TomatoTFLiteClassifier", "Model ready: ${isModelReady()}")
        Log.d("TomatoTFLiteClassifier", "Input bitmap size: ${bitmap.width}x${bitmap.height}")

        try {
            // Check if model is ready
            if (!isModelReady()) {
                Log.w("TomatoTFLiteClassifier", "Model not ready for real-time classification")
                return ClassificationResult(
                    disease = "Model Loading...",
                    confidence = 0f,
                    allConfidences = FloatArray(labels.size),
                    isHighConfidence = false,
                    preprocessingQuality = 0f
                )
            }

            // Validate input bitmap
            if (bitmap.isRecycled) {
                Log.w("TomatoTFLiteClassifier", "Input bitmap is recycled")
                return ClassificationResult(
                    disease = "Error",
                    confidence = 0f,
                    allConfidences = FloatArray(labels.size),
                    isHighConfidence = false,
                    preprocessingQuality = 0f
                )
            }

            // Simplified preprocessing for real-time performance
            val resizedBitmap = Bitmap.createScaledBitmap(bitmap, inputImageWidth, inputImageHeight, false)
            Log.d("TomatoTFLiteClassifier", "Bitmap resized to: ${resizedBitmap.width}x${resizedBitmap.height}")

            val inputBuffer = convertBitmapToTensorBuffer(resizedBitmap)
            Log.d("TomatoTFLiteClassifier", "Input tensor buffer created successfully")

            // Run inference with timeout protection
            val outputs = model?.process(inputBuffer)
            val confidences = outputs?.getOutputFeature0AsTensorBuffer()?.floatArray ?: FloatArray(labels.size)
            Log.d("TomatoTFLiteClassifier", "Model inference completed. Output size: ${confidences.size}")

            // Validate output
            if (confidences.isEmpty() || confidences.all { it.isNaN() || it.isInfinite() }) {
                Log.w("TomatoTFLiteClassifier", "Invalid model output detected")
                return ClassificationResult(
                    disease = "Processing Error",
                    confidence = 0f,
                    allConfidences = FloatArray(labels.size),
                    isHighConfidence = false,
                    preprocessingQuality = 0f
                )
            }

            // Process results
            val maxIdx = confidences.indices.maxByOrNull { confidences[it] } ?: -1
            val maxConfidence = if (maxIdx >= 0) confidences[maxIdx] else 0f

            Log.d("TomatoTFLiteClassifier", "Max confidence index: $maxIdx, confidence: $maxConfidence")
            if (maxIdx >= 0 && maxIdx < labels.size) {
                Log.d("TomatoTFLiteClassifier", "Predicted disease: ${labels[maxIdx]}")
            }

            // Apply confidence-based filtering for real-time
            val finalDisease = when {
                maxIdx < 0 || maxIdx >= labels.size -> "Unknown"
                maxConfidence < 0.3f -> "Analyzing..." // Lower threshold for real-time
                else -> labels[maxIdx]
            }

            val isHighConfidence = maxConfidence >= highConfidenceThreshold

            Log.d("TomatoTFLiteClassifier", "Final result - Disease: $finalDisease, Confidence: $maxConfidence, High confidence: $isHighConfidence")

            return ClassificationResult(
                disease = finalDisease,
                confidence = maxConfidence,
                allConfidences = confidences,
                isHighConfidence = isHighConfidence,
                preprocessingQuality = 0.8f // Assume good quality for real-time
            )

        } catch (e: Exception) {
            Log.e("TomatoTFLiteClassifier", "Error in real-time classification: ${e.message}", e)
            Log.e("TomatoTFLiteClassifier", "Model ready status during error: ${isModelReady()}")
            e.printStackTrace()
            return ClassificationResult(
                disease = "Error",
                confidence = 0f,
                allConfidences = FloatArray(labels.size),
                isHighConfidence = false,
                preprocessingQuality = 0f
            )
        }
    }

    /**
     * Check if the model is properly loaded and ready for inference
     */
    fun isModelReady(): Boolean = model != null

    /**
     * Clean up resources
     */
    fun close() {
        model?.close()
        model = null
        Log.d("TomatoTFLiteClassifier", "Model resources cleaned up")
    }
}
