package com.ml.tomatoscan.data

import android.util.Log

data class RecommendationSet(
    val immediateActions: List<String>,
    val treatmentPlan: List<String>,
    val preventionMeasures: List<String>,
    val monitoringGuidance: List<String>,
    val expertConsultation: List<String>,
    val confidenceIndicators: List<String>
)

class RecommendationEngine {
    
    companion object {
        private const val TAG = "RecommendationEngine"
        
        // Treatment databases for different diseases
        private val FUNGAL_TREATMENTS = mapOf(
            "Early Blight" to listOf(
                "Apply copper-based fungicide every 7-10 days",
                "Remove affected leaves and dispose properly",
                "Improve air circulation around plants",
                "Avoid overhead watering"
            ),
            "Late Blight" to listOf(
                "Apply systemic fungicide immediately",
                "Remove all affected plant parts",
                "Increase plant spacing for air flow",
                "Consider resistant varieties for future planting"
            ),
            "Septoria Leaf Spot" to listOf(
                "Apply chlorothalonil or copper fungicide",
                "Remove lower leaves touching soil",
                "Mulch around plants to prevent soil splash",
                "Water at soil level only"
            ),
            "Powdery Mildew" to listOf(
                "Apply sulfur-based fungicide",
                "Improve air circulation",
                "Reduce humidity around plants",
                "Apply baking soda solution (1 tsp per quart water)"
            )
        )
        
        private val BACTERIAL_TREATMENTS = mapOf(
            "Bacterial Spot" to listOf(
                "Apply copper-based bactericide",
                "Remove affected leaves immediately",
                "Disinfect tools between plants",
                "Avoid working with wet plants"
            )
        )
        
        private val VIRAL_TREATMENTS = mapOf(
            "Mosaic Virus" to listOf(
                "Remove infected plants immediately",
                "Control aphid vectors with insecticides",
                "Disinfect tools with 10% bleach solution",
                "Plant virus-resistant varieties"
            ),
            "Leaf Curl" to listOf(
                "Remove infected plants to prevent spread",
                "Control whitefly vectors",
                "Use reflective mulch to deter insects",
                "Plant resistant varieties if available"
            )
        )
        
        private val NUTRITIONAL_TREATMENTS = mapOf(
            "Nutrient Deficiency" to listOf(
                "Conduct soil test to identify specific deficiency",
                "Apply balanced fertilizer (10-10-10)",
                "Ensure proper soil pH (6.0-6.8)",
                "Consider foliar feeding for quick response"
            )
        )
    }
    
    fun generateRecommendations(ensembleResult: EnsembleResult): RecommendationSet {
        try {
            Log.d(TAG, "Generating recommendations for: ${ensembleResult.finalDisease}")
            
            val disease = ensembleResult.finalDisease
            val confidence = ensembleResult.finalConfidence
            val severity = ensembleResult.severityAnalysis
            val consensus = ensembleResult.consensusLevel
            
            // Generate different types of recommendations
            val immediateActions = generateImmediateActions(disease, severity, confidence)
            val treatmentPlan = generateTreatmentPlan(disease, severity, confidence)
            val preventionMeasures = generatePreventionMeasures(disease, ensembleResult.geminiResult)
            val monitoringGuidance = generateMonitoringGuidance(severity, disease)
            val expertConsultation = generateExpertConsultation(confidence, consensus, severity)
            val confidenceIndicators = generateConfidenceIndicators(ensembleResult)
            
            return RecommendationSet(
                immediateActions = immediateActions,
                treatmentPlan = treatmentPlan,
                preventionMeasures = preventionMeasures,
                monitoringGuidance = monitoringGuidance,
                expertConsultation = expertConsultation,
                confidenceIndicators = confidenceIndicators
            )
            
        } catch (e: Exception) {
            Log.e(TAG, "Error generating recommendations", e)
            return getDefaultRecommendations()
        }
    }
    
    private fun generateImmediateActions(
        disease: String, 
        severity: SeverityAnalysis?, 
        confidence: Float
    ): List<String> {
        val actions = mutableListOf<String>()
        
        // Confidence-based immediate actions
        when {
            confidence >= 0.8f -> {
                actions.add("🎯 High confidence diagnosis - proceed with treatment")
            }
            confidence >= 0.6f -> {
                actions.add("🔍 Moderate confidence - monitor closely and begin treatment")
            }
            else -> {
                actions.add("⚠️ Low confidence - consider retaking image or expert consultation")
            }
        }
        
        // Severity-based immediate actions
        severity?.let { sev ->
            when (sev.urgencyLevel) {
                "Critical" -> {
                    actions.add("🚨 IMMEDIATE: Isolate plant to prevent spread")
                    actions.add("🚨 IMMEDIATE: Begin treatment within 24 hours")
                }
                "High" -> {
                    actions.add("⚠️ URGENT: Start treatment within 2-3 days")
                    actions.add("⚠️ URGENT: Remove affected plant parts")
                }
                "Medium" -> {
                    actions.add("📋 MODERATE: Plan treatment within 1 week")
                }
                "Low" -> {
                    actions.add("ℹ️ LOW: Monitor and treat as symptoms develop")
                }
            }
        }
        
        // Disease-specific immediate actions
        when {
            disease.equals("Healthy", ignoreCase = true) -> {
                actions.add("✅ Plant appears healthy - continue regular care")
            }
            disease.contains("Blight", ignoreCase = true) -> {
                actions.add("🍃 Remove affected leaves immediately")
                actions.add("💧 Stop overhead watering")
            }
            disease.contains("Bacterial", ignoreCase = true) -> {
                actions.add("🧽 Disinfect tools before and after use")
                actions.add("🚫 Avoid working with wet plants")
            }
            disease.contains("Virus", ignoreCase = true) -> {
                actions.add("🦠 Isolate plant immediately")
                actions.add("🐛 Control insect vectors")
            }
        }
        
        return actions
    }
    
    private fun generateTreatmentPlan(
        disease: String, 
        severity: SeverityAnalysis?, 
        confidence: Float
    ): List<String> {
        val treatments = mutableListOf<String>()
        
        // Get disease-specific treatments
        val specificTreatments = when {
            disease in FUNGAL_TREATMENTS -> FUNGAL_TREATMENTS[disease] ?: emptyList()
            disease in BACTERIAL_TREATMENTS -> BACTERIAL_TREATMENTS[disease] ?: emptyList()
            disease in VIRAL_TREATMENTS -> VIRAL_TREATMENTS[disease] ?: emptyList()
            disease in NUTRITIONAL_TREATMENTS -> NUTRITIONAL_TREATMENTS[disease] ?: emptyList()
            else -> getGenericTreatments(disease)
        }
        
        treatments.addAll(specificTreatments)
        
        // Confidence-based treatment modifications
        if (confidence < 0.6f) {
            treatments.add("⚠️ Due to diagnostic uncertainty, start with gentler treatments")
            treatments.add("📊 Monitor plant response before escalating treatment")
        }
        
        // Severity-based treatment intensity
        severity?.let { sev ->
            when (sev.severityLevel) {
                "Critical", "Severe" -> {
                    treatments.add("💪 Use maximum label rates for treatments")
                    treatments.add("🔄 Repeat treatments every 5-7 days")
                }
                "Moderate" -> {
                    treatments.add("📏 Use standard label rates")
                    treatments.add("🔄 Repeat treatments every 7-10 days")
                }
                "Mild" -> {
                    treatments.add("🌱 Start with organic treatments if available")
                    treatments.add("🔄 Repeat treatments every 10-14 days")
                }
            }
        }
        
        return treatments
    }
    
    private fun generatePreventionMeasures(
        disease: String, 
        geminiResult: TomatoAnalysisResult
    ): List<String> {
        val prevention = mutableListOf<String>()
        
        // Use Gemini's prevention measures as base
        prevention.addAll(geminiResult.preventionMeasures)
        
        // Add disease-specific prevention
        when {
            disease.contains("Blight", ignoreCase = true) -> {
                prevention.addAll(listOf(
                    "🌬️ Ensure good air circulation between plants",
                    "💧 Water at soil level, avoid wetting leaves",
                    "🍂 Remove plant debris at end of season",
                    "🔄 Rotate crops annually"
                ))
            }
            disease.contains("Bacterial", ignoreCase = true) -> {
                prevention.addAll(listOf(
                    "🧽 Sanitize tools between plants",
                    "🚫 Avoid working with wet plants",
                    "🌱 Use disease-free seeds and transplants",
                    "💨 Improve air circulation"
                ))
            }
            disease.contains("Virus", ignoreCase = true) -> {
                prevention.addAll(listOf(
                    "🐛 Control insect vectors (aphids, whiteflies)",
                    "🌱 Use certified virus-free plants",
                    "🧤 Wash hands between handling plants",
                    "🚫 Remove weeds that harbor viruses"
                ))
            }
        }
        
        return prevention.distinct()
    }
    
    private fun generateMonitoringGuidance(
        severity: SeverityAnalysis?, 
        disease: String
    ): List<String> {
        val monitoring = mutableListOf<String>()
        
        severity?.let { sev ->
            monitoring.add("📅 ${sev.monitoringFrequency}")
            monitoring.add("📈 Expected progression: ${sev.expectedProgression}")
        }
        
        // Disease-specific monitoring
        when {
            disease.contains("Blight", ignoreCase = true) -> {
                monitoring.addAll(listOf(
                    "🔍 Check for new spots on leaves and stems",
                    "🌡️ Monitor weather conditions (humidity, temperature)",
                    "📸 Take photos to track progression"
                ))
            }
            disease.contains("Virus", ignoreCase = true) -> {
                monitoring.addAll(listOf(
                    "🦠 Watch for symptom spread to other plants",
                    "🐛 Monitor for insect vector activity",
                    "📊 Track plant growth and fruit production"
                ))
            }
            else -> {
                monitoring.addAll(listOf(
                    "👀 Inspect plants regularly for changes",
                    "📝 Keep treatment records",
                    "🌱 Monitor new growth for symptoms"
                ))
            }
        }
        
        return monitoring
    }
    
    private fun generateExpertConsultation(
        confidence: Float, 
        consensus: String, 
        severity: SeverityAnalysis?
    ): List<String> {
        val consultation = mutableListOf<String>()
        
        // Confidence-based consultation recommendations
        when {
            confidence < 0.4f -> {
                consultation.add("🎓 RECOMMENDED: Consult with plant pathologist due to low diagnostic confidence")
            }
            confidence < 0.6f && consensus in listOf("Low", "Very Low") -> {
                consultation.add("🤔 CONSIDER: Expert consultation due to model disagreement")
            }
        }
        
        // Severity-based consultation
        severity?.let { sev ->
            when (sev.urgencyLevel) {
                "Critical" -> {
                    consultation.add("🚨 URGENT: Contact agricultural extension agent immediately")
                }
                "High" -> {
                    consultation.add("📞 Contact local agricultural expert within 48 hours")
                }
            }
        }
        
        // General consultation resources
        consultation.addAll(listOf(
            "🏢 Contact your local agricultural extension office",
            "🌐 Submit sample to plant diagnostic lab if symptoms worsen",
            "👥 Join local gardening groups for community support"
        ))
        
        return consultation
    }
    
    private fun generateConfidenceIndicators(ensembleResult: EnsembleResult): List<String> {
        val indicators = mutableListOf<String>()
        val metrics = ensembleResult.analysisMetrics
        
        indicators.add("🎯 Diagnosis Confidence: ${String.format("%.1f", ensembleResult.finalConfidence * 100)}%")
        indicators.add("🤝 Model Agreement: ${String.format("%.1f", metrics.modelAgreement * 100)}%")
        indicators.add("📸 Image Quality: ${String.format("%.1f", metrics.imageQuality * 100)}%")
        indicators.add("🔬 Overall Reliability: ${String.format("%.1f", metrics.overallReliability * 100)}%")
        indicators.add("📊 Consensus Level: ${ensembleResult.consensusLevel}")
        
        return indicators
    }
    
    private fun getGenericTreatments(disease: String): List<String> {
        return listOf(
            "🌱 Improve general plant health with proper nutrition",
            "💧 Ensure proper watering practices",
            "✂️ Remove affected plant parts",
            "🌬️ Improve air circulation around plants"
        )
    }
    
    private fun getDefaultRecommendations(): RecommendationSet {
        return RecommendationSet(
            immediateActions = listOf("⚠️ Analysis incomplete - retake image with better lighting"),
            treatmentPlan = listOf("🔍 Consult with local agricultural expert"),
            preventionMeasures = listOf("🌱 Maintain good plant hygiene", "💧 Water properly"),
            monitoringGuidance = listOf("👀 Monitor plants regularly"),
            expertConsultation = listOf("📞 Contact agricultural extension office"),
            confidenceIndicators = listOf("❌ Analysis failed - no confidence metrics available")
        )
    }
}
