1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ml.tomatoscan"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-feature
11-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:5-85
12        android:name="android.hardware.camera"
12-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:19-57
13        android:required="false" />
13-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:5:58-82
14
15    <uses-permission android:name="android.permission.CAMERA" />
15-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:6:5-65
15-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:6:22-62
16    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
16-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:8:5-76
16-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:8:22-73
17    <uses-permission
17-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:9:5-10:38
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:9:22-77
19        android:maxSdkVersion="32" />
19-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:10:9-35
20    <uses-permission android:name="android.permission.INTERNET" />
20-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:5-67
20-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:25:22-64
21    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
21-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:5-79
21-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:26:22-76
22    <uses-permission android:name="android.permission.WAKE_LOCK" />
22-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:25:5-68
22-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:25:22-65
23    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
23-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:26:5-110
23-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:26:22-107
24    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
24-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:25:5-79
24-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:25:22-76
25    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
25-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:26:5-88
25-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:26:22-85
26    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
26-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:27:5-82
26-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:27:22-79
27    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3121473f0d7729e2f27f26f691d5e1\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
27-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\3c3121473f0d7729e2f27f26f691d5e1\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
28
29    <permission
29-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
30        android:name="com.ml.tomatoscan.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
30-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
31        android:protectionLevel="signature" />
31-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
32
33    <uses-permission android:name="com.ml.tomatoscan.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
33-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
34
35    <application
35-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:12:5-44:19
36        android:name="com.ml.tomatoscan.TomatoScanApplication"
36-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:13:9-46
37        android:allowBackup="true"
37-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:14:9-35
38        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
38-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b041789a1e4acdae9799d04a01c20569\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
39        android:dataExtractionRules="@xml/data_extraction_rules"
39-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:15:9-65
40        android:debuggable="true"
41        android:extractNativeLibs="false"
42        android:fullBackupContent="@xml/backup_rules"
42-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:16:9-54
43        android:icon="@mipmap/ic_launcher"
43-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:17:9-43
44        android:label="@string/app_name"
44-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:18:9-41
45        android:roundIcon="@mipmap/ic_launcher_round"
45-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:19:9-54
46        android:supportsRtl="true"
46-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:20:9-35
47        android:testOnly="true"
48        android:theme="@style/Theme.TomatoScan" >
48-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:21:9-48
49        <activity
49-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:23:9-33:20
50            android:name="com.ml.tomatoscan.MainActivity"
50-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:24:13-41
51            android:exported="true"
51-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:25:13-36
52            android:label="@string/app_name"
52-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:26:13-45
53            android:theme="@style/Theme.TomatoScan" >
53-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:27:13-52
54            <intent-filter>
54-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:28:13-32:29
55                <action android:name="android.intent.action.MAIN" />
55-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:29:17-69
55-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:29:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:31:17-77
57-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:31:27-74
58            </intent-filter>
59        </activity>
60
61        <provider
62            android:name="androidx.core.content.FileProvider"
62-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:36:13-62
63            android:authorities="com.ml.tomatoscan.provider"
63-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:37:13-60
64            android:exported="false"
64-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:38:13-37
65            android:grantUriPermissions="true" >
65-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:39:13-47
66            <meta-data
66-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:40:13-42:54
67                android:name="android.support.FILE_PROVIDER_PATHS"
67-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:41:17-67
68                android:resource="@xml/file_paths" />
68-->C:\Users\<USER>\AndroidStudioProjects\TomatoScan\app\src\main\AndroidManifest.xml:42:17-51
69        </provider>
70
71        <activity
71-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0bd736772407a73d3c97f430011aa11\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
72            android:name="androidx.compose.ui.tooling.PreviewActivity"
72-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0bd736772407a73d3c97f430011aa11\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
73            android:exported="true" />
73-->[androidx.compose.ui:ui-tooling-android:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\d0bd736772407a73d3c97f430011aa11\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
74
75        <service
75-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:24:9-33:19
76            android:name="androidx.camera.core.impl.MetadataHolderService"
76-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:25:13-75
77            android:enabled="false"
77-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:26:13-36
78            android:exported="false" >
78-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:27:13-37
79            <meta-data
79-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:30:13-32:89
80                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
80-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:31:17-103
81                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
81-->[androidx.camera:camera-camera2:1.3.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\3aad9522d0e906accaf9f8684138511f\transformed\camera-camera2-1.3.4\AndroidManifest.xml:32:17-86
82        </service>
83
84        <activity
84-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:29:9-46:20
85            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
85-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:30:13-80
86            android:excludeFromRecents="true"
86-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:31:13-46
87            android:exported="true"
87-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:32:13-36
88            android:launchMode="singleTask"
88-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:33:13-44
89            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
89-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:34:13-72
90            <intent-filter>
90-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:35:13-45:29
91                <action android:name="android.intent.action.VIEW" />
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
91-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
92
93                <category android:name="android.intent.category.DEFAULT" />
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
93-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
94                <category android:name="android.intent.category.BROWSABLE" />
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
94-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
95
96                <data
96-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
97                    android:host="firebase.auth"
97-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
98                    android:path="/"
98-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
99                    android:scheme="genericidp" />
99-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
100            </intent-filter>
101        </activity>
102        <activity
102-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:47:9-64:20
103            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
103-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:48:13-79
104            android:excludeFromRecents="true"
104-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:49:13-46
105            android:exported="true"
105-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:50:13-36
106            android:launchMode="singleTask"
106-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:51:13-44
107            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
107-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:52:13-72
108            <intent-filter>
108-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:53:13-63:29
109                <action android:name="android.intent.action.VIEW" />
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:17-69
109-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:36:25-66
110
111                <category android:name="android.intent.category.DEFAULT" />
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:17-76
111-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:38:27-73
112                <category android:name="android.intent.category.BROWSABLE" />
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:17-78
112-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:39:27-75
113
114                <data
114-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:41:17-44:51
115                    android:host="firebase.auth"
115-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:42:21-49
116                    android:path="/"
116-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:43:21-37
117                    android:scheme="recaptcha" />
117-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:44:21-48
118            </intent-filter>
119        </activity>
120
121        <service
121-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:66:9-72:19
122            android:name="com.google.firebase.components.ComponentDiscoveryService"
122-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:67:13-84
123            android:directBootAware="true"
123-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
124            android:exported="false" >
124-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:68:13-37
125            <meta-data
125-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:69:13-71:85
126                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
126-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:70:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-auth:23.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39146c846590e92b82ff993166972c07\transformed\firebase-auth-23.0.0\AndroidManifest.xml:71:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:17:13-19:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
129-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:18:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:19:17-82
131            <meta-data
131-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:20:13-22:85
132                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
132-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:21:17-111
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-firestore:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c4364ef940c60315aad2f1486d5d6111\transformed\firebase-firestore-25.0.0\AndroidManifest.xml:22:17-82
134            <meta-data
134-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:37:13-39:85
135                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
135-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:38:17-139
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:39:17-82
137            <meta-data
137-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:30:13-32:85
138                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
138-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:31:17-118
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:32:17-82
140            <meta-data
140-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:33:13-35:85
141                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
141-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:34:17-107
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-storage:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\b4dca87163af47a2a5751b4475e90ef9\transformed\firebase-storage-21.0.0\AndroidManifest.xml:35:17-82
143            <meta-data
143-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
144                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
144-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
146            <meta-data
146-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
147                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
147-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c2be6a55ecbb5e85bb90a11cc0bb0737\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
149            <meta-data
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
150                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
152            <meta-data
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
153                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
153-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4c0772d81d355f2b4ae6290a2715e77a\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
155            <meta-data
155-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32712392ecaa9a10a8bb52f3f0cde016\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
156                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
156-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32712392ecaa9a10a8bb52f3f0cde016\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\32712392ecaa9a10a8bb52f3f0cde016\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
158            <meta-data
158-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
159                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
159-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
161        </service>
162
163        <receiver
163-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:29:9-33:20
164            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
164-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:30:13-85
165            android:enabled="true"
165-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:31:13-35
166            android:exported="false" >
166-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:32:13-37
167        </receiver>
168
169        <service
169-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:35:9-38:40
170            android:name="com.google.android.gms.measurement.AppMeasurementService"
170-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:36:13-84
171            android:enabled="true"
171-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:37:13-35
172            android:exported="false" />
172-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:38:13-37
173        <service
173-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:39:9-43:72
174            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
174-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:40:13-87
175            android:enabled="true"
175-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:41:13-35
176            android:exported="false"
176-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:42:13-37
177            android:permission="android.permission.BIND_JOB_SERVICE" />
177-->[com.google.android.gms:play-services-measurement:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4e6714eca6c740dfd139b463b3cf37c6\transformed\play-services-measurement-22.0.0\AndroidManifest.xml:43:13-69
178
179        <property
179-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:30:9-32:61
180            android:name="android.adservices.AD_SERVICES_CONFIG"
180-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:31:13-65
181            android:resource="@xml/ga_ad_services_config" />
181-->[com.google.android.gms:play-services-measurement-api:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\0fb91064c785e307ea1a6b994ed7f953\transformed\play-services-measurement-api-22.0.0\AndroidManifest.xml:32:13-58
182
183        <service
183-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
184            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
184-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
185            android:enabled="true"
185-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
186            android:exported="false" >
186-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
187            <meta-data
187-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
188                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
188-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
189                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
189-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
190        </service>
191
192        <activity
192-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
193            android:name="androidx.credentials.playservices.HiddenActivity"
193-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
194            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
194-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
195            android:enabled="true"
195-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
196            android:exported="false"
196-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
197            android:fitsSystemWindows="true"
197-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
198            android:theme="@style/Theme.Hidden" >
198-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\e781f7281c7580c60a0b79b3a9e8dbd7\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
199        </activity>
200        <activity
200-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
201            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
201-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
202            android:excludeFromRecents="true"
202-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
203            android:exported="false"
203-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
204            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
204-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
205        <!--
206            Service handling Google Sign-In user revocation. For apps that do not integrate with
207            Google Sign-In, this service will never be started.
208        -->
209        <service
209-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
210            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
210-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
211            android:exported="true"
211-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
212            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
212-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
213            android:visibleToInstantApps="true" />
213-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\244997e3cc51fcf836718dd903db41cf\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
214
215        <provider
215-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
216            android:name="com.google.firebase.provider.FirebaseInitProvider"
216-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
217            android:authorities="com.ml.tomatoscan.firebaseinitprovider"
217-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
218            android:directBootAware="true"
218-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
219            android:exported="false"
219-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
220            android:initOrder="100" />
220-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\f4d4e078114438bbe1ab1575772d723a\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
221
222        <service
222-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7deefaa41b06c65f51137fa46bab1d85\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
223            android:name="androidx.room.MultiInstanceInvalidationService"
223-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7deefaa41b06c65f51137fa46bab1d85\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
224            android:directBootAware="true"
224-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7deefaa41b06c65f51137fa46bab1d85\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
225            android:exported="false" />
225-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\7deefaa41b06c65f51137fa46bab1d85\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
226
227        <activity
227-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c37016efe0305cea6bd6038fcb93f39a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
228            android:name="com.google.android.gms.common.api.GoogleApiActivity"
228-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c37016efe0305cea6bd6038fcb93f39a\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
229            android:exported="false"
229-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c37016efe0305cea6bd6038fcb93f39a\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
230            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
230-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c37016efe0305cea6bd6038fcb93f39a\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
231
232        <provider
232-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
233            android:name="androidx.startup.InitializationProvider"
233-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
234            android:authorities="com.ml.tomatoscan.androidx-startup"
234-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
235            android:exported="false" >
235-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
236            <meta-data
236-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
237                android:name="androidx.emoji2.text.EmojiCompatInitializer"
237-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
238                android:value="androidx.startup" />
238-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4ed03623b73ece405f3018454afa4142\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
239            <meta-data
239-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a01543214f17e8614389df9ca1a5d3b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:29:13-31:52
240                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
240-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a01543214f17e8614389df9ca1a5d3b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:30:17-78
241                android:value="androidx.startup" />
241-->[androidx.lifecycle:lifecycle-process:2.9.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\a01543214f17e8614389df9ca1a5d3b6\transformed\lifecycle-process-2.9.1\AndroidManifest.xml:31:17-49
242            <meta-data
242-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
243                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
243-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
244                android:value="androidx.startup" />
244-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
245        </provider>
246
247        <receiver
247-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
248            android:name="androidx.profileinstaller.ProfileInstallReceiver"
248-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
249            android:directBootAware="false"
249-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
250            android:enabled="true"
250-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
251            android:exported="true"
251-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
252            android:permission="android.permission.DUMP" >
252-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
253            <intent-filter>
253-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
254                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
254-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
255            </intent-filter>
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
257                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
257-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
258            </intent-filter>
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
260                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
260-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
260-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
263                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
263-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
263-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\48e277b0467fb7350c5b7f104cd9cbfe\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
264            </intent-filter>
265        </receiver>
266
267        <uses-library
267-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbd3655e3442130688378d8c184b6f1f\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
268            android:name="android.ext.adservices"
268-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbd3655e3442130688378d8c184b6f1f\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
269            android:required="false" />
269-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbd3655e3442130688378d8c184b6f1f\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
270
271        <meta-data
271-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93656968bc1a0c41915c9808633de12a\transformed\play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
272            android:name="com.google.android.gms.version"
272-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93656968bc1a0c41915c9808633de12a\transformed\play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
273            android:value="@integer/google_play_services_version" />
273-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93656968bc1a0c41915c9808633de12a\transformed\play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
274
275        <activity
275-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e38ec7a086dc177d2e83d1125b170f\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:23:9-26:79
276            android:name="androidx.activity.ComponentActivity"
276-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e38ec7a086dc177d2e83d1125b170f\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:24:13-63
277            android:exported="true"
277-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e38ec7a086dc177d2e83d1125b170f\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:25:13-36
278            android:theme="@android:style/Theme.Material.Light.NoActionBar" />
278-->[androidx.compose.ui:ui-test-manifest:1.8.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\24e38ec7a086dc177d2e83d1125b170f\transformed\ui-test-manifest-1.8.2\AndroidManifest.xml:26:13-76
279    </application>
280
281</manifest>
