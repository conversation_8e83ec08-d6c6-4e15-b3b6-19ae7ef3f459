package com.ml.tomatoscan.viewmodels

import android.app.Application
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

enum class Language(val code: String) {
    ENGLISH("en"),
    FILIPINO("fil"),
    BISAYA("ceb")
}

private val Application.languageDataStore: DataStore<Preferences> by preferencesDataStore(name = "language_settings")

class LanguageViewModel(private val application: Application) : ViewModel() {

    private val languagePreferenceKey = stringPreferencesKey("language_code")

    val language: StateFlow<Language> = application.languageDataStore.data
        .map {
            val langCode = it[languagePreferenceKey] ?: Language.ENGLISH.code
            Language.values().find { l -> l.code == langCode } ?: Language.ENGLISH
        }
        .stateIn(
            scope = viewModelScope,
            started = SharingStarted.WhileSubscribed(5_000),
            initialValue = Language.ENGLISH
        )

    fun setLanguage(newLanguage: Language) {
        viewModelScope.launch {
            application.languageDataStore.edit {
                it[languagePreferenceKey] = newLanguage.code
            }
        }
    }
}

class LanguageViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(LanguageViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return LanguageViewModel(application) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
